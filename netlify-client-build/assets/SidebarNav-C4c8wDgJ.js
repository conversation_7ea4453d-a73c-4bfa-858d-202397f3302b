import{l as e,Z as a,j as s,aT as i,M as l,aU as r,am as t,o,p as n,a as d,V as m,F as c,I as p,J as b,T as x,t as j}from"./ui-d9gyv7ZA.js";import{u as h,a as u,b as g,F as f,c as y,d as v,e as z,f as C,g as k,h as w,i as S,j as I,k as R,l as T}from"./index-CzelJEcS.js";import{useSidebar as W}from"./NavBar-Bw-7DSmJ.js";import"./vendor-BJbfb-22.js";import"./icons-wvpQ6TvE.js";import"./utils-DuWhH-qE.js";import"./Logo-A6zVQR6z.js";const F=[{label:"Dashboard",icon:f,path:"/dashboard"}],M=[{label:"Cover Letters",icon:y,path:"/cover-letters"},{label:"Resume",icon:v,path:"/resume"},{label:"Jobs",icon:z,path:"/jobs"}],A=[{label:"Interview Prep",icon:C,path:"/interview"},{label:"Learning",icon:k,path:"/learning"},{label:"Tracker",icon:w,path:"/tracker"}],L=[{label:"Profile",icon:I,path:"/profile"}],_=[{label:"Admin Dashboard",icon:R,path:"/admin"}];function B(){const f=e("white","gray.900"),y=e("blue.50","gray.700"),v=e("gray.700","gray.200"),z=e("gray.200","gray.700"),{data:C}=h(),k=u(),w=g(),{isSidebarOpen:I,closeSidebar:R,isCollapsed:B,toggleCollapse:D}=W(),E=a({base:!0,md:!1}),J=C?.email?.includes("admin")||C?.email?.endsWith("@admin.careerdart.com"),O=({item:a})=>{const i=w.pathname===a.path,l=e("blue.50","blue.900"),r=e("blue.600","blue.300");return s.jsxs(d,{as:"button",onClick:()=>{return e=a.path,void(C||"/"===e?(k(e),E&&R()):k("/login"));var e},display:"flex",alignItems:"center",padding:"0.5rem",borderRadius:"0.375rem",color:i?r:v,bg:i?l:"transparent",width:"100%",textAlign:"left",border:"none",cursor:"pointer",_hover:{bg:i?l:y},transition:"all 0.2s",title:B?a.label:void 0,children:[s.jsx(j,{as:a.icon,mr:B?0:3,color:"blue.500",boxSize:5}),!B&&s.jsx(x,{fontWeight:i?"semibold":"medium",children:a.label})]},a.label)},P=({title:e})=>B?s.jsx(b,{my:2}):s.jsxs(c,{justify:"space-between",align:"center",mb:1,mt:3,px:1,children:[s.jsx(x,{fontSize:"xs",fontWeight:"medium",textTransform:"uppercase",color:"gray.500",_dark:{color:"gray.400"},letterSpacing:"wider",children:e}),"Main"===e&&s.jsx(p,{"aria-label":B?"Expand sidebar":"Collapse sidebar",icon:s.jsx(T,{size:"10px"}),size:"xs",onClick:D,variant:"ghost",colorScheme:"blue",borderRadius:"full"})]}),U=()=>s.jsxs(m,{align:"stretch",spacing:2,mt:2,children:[B&&s.jsx(c,{justify:"center",mb:3,children:s.jsx(p,{"aria-label":"Expand sidebar",icon:s.jsx(S,{size:"12px"}),size:"sm",onClick:D,variant:"ghost",colorScheme:"blue",borderRadius:"full"})}),s.jsx(P,{title:"Main"}),F.map((e=>s.jsx(O,{item:e},e.label))),s.jsx(b,{my:2}),s.jsx(P,{title:"Content"}),M.map((e=>s.jsx(O,{item:e},e.label))),s.jsx(b,{my:2}),s.jsx(P,{title:"Tools & Resources"}),A.map((e=>s.jsx(O,{item:e},e.label))),C&&s.jsxs(s.Fragment,{children:[s.jsx(b,{my:2}),s.jsx(P,{title:"User"}),L.map((e=>s.jsx(O,{item:e},e.label)))]}),C&&J&&s.jsxs(s.Fragment,{children:[s.jsx(b,{my:2}),s.jsx(P,{title:"Admin"}),_.map((e=>s.jsx(O,{item:e},e.label)))]})]});return E?s.jsxs(i,{isOpen:I,placement:"left",onClose:R,size:"xs",children:[s.jsx(l,{}),s.jsxs(r,{bg:f,zIndex:4,children:[s.jsx(t,{}),s.jsx(o,{borderBottomWidth:"1px",borderColor:z,py:3,px:4,fontSize:"md",fontWeight:"semibold",color:"blue.600",_dark:{color:"blue.300"},children:"Menu"}),s.jsx(n,{px:3,py:2,children:s.jsx(U,{})})]})]}):s.jsx(d,{as:"nav",w:B?"60px":"220px",bg:f,h:"100vh",p:B?2:4,boxShadow:"md",position:"fixed",top:"0",left:"0",zIndex:"5",pt:"80px",display:{base:"none",md:"/"!==w.pathname?"block":"none"},transition:"width 0.3s ease, padding 0.3s ease",children:s.jsx(U,{})})}export{B as default};
