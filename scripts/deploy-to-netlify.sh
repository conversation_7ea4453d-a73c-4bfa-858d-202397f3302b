#!/bin/bash

# CareerDart Netlify Deployment Script
# This script builds the frontend and deploys it to Netlify using pre-built files

set -e

echo "🔥 Starting CareerDart Netlify deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [[ ! -f "main.wasp" ]]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

print_info "Building Wasp application..."

# Clean previous builds
print_info "Cleaning previous builds..."
rm -rf .wasp/build
rm -rf netlify-client-build

# Build the Wasp application
wasp build

print_success "Wasp build completed"

# Navigate to the web app build directory
cd .wasp/build/web-app

print_info "Installing production dependencies..."
npm install

print_info "Building frontend with Netlify-optimized configuration..."

# Set environment variables for Netlify deployment
# Production: careerdart.com -> careerdart.netlify.app
export NODE_ENV=production
export REACT_APP_API_URL="https://careerdart-prod.fly.dev"
export WASP_WEB_CLIENT_URL="https://careerdart.com"
export WASP_SERVER_URL="https://careerdart-prod.fly.dev"
export GENERATE_SOURCEMAP=false
export SKIP_PREFLIGHT_CHECK=true
export CI=true

# Build the frontend
npm run build

print_success "Frontend build completed"

# Navigate back to project root
cd ../../../

# Create netlify deployment directory
print_info "Preparing Netlify deployment files..."
mkdir -p netlify-client-build

# Copy built files to netlify deployment directory
cp -r .wasp/build/web-app/build/* netlify-client-build/

# Copy Lighthouse reports for admin dashboard
if ls lighthouse*.json 1> /dev/null 2>&1; then
    print_info "Copying Lighthouse reports..."
    cp lighthouse*.json netlify-client-build/ 2>/dev/null || true
fi

# Add redirects file if it doesn't exist (backup to netlify.toml)
if [[ ! -f "netlify-client-build/_redirects" ]]; then
    print_info "Creating _redirects file..."
    cat > netlify-client-build/_redirects << 'EOF'
# API redirects to backend
/api/* https://careerdart-prod.fly.dev/api/:splat 200
/auth/* https://careerdart-prod.fly.dev/auth/:splat 200
/operations/* https://careerdart-prod.fly.dev/operations/:splat 200

# SPA routing
/* /index.html 200
EOF
fi

print_success "Deployment files prepared in netlify-client-build/"

# Display build information
print_info "Build Summary:"
echo "📁 Build directory: netlify-client-build/"
echo "🌐 API URL: Relative (uses Netlify redirects)"
echo "🚀 Environment: Production"
echo "📊 Source maps: Disabled"

print_warning "Important: Make sure your Netlify site is configured to:"
echo "  • Publish directory: netlify-client-build"
echo "  • Build command: echo 'Using prebuilt files'"
echo "  • API redirects are configured in netlify.toml"

print_success "🎉 Build completed! Ready for Netlify deployment"
print_info "To deploy: Push to your connected Git repository or use Netlify CLI"

# Optional: Deploy directly with Netlify CLI if available
if command -v netlify &> /dev/null; then
    echo ""
    read -p "Deploy to Netlify now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deploying to Netlify..."
        netlify deploy --dir=netlify-client-build --prod
        print_success "Deployed to Netlify!"
    fi
else
    print_warning "Netlify CLI not found. Install with: npm install -g netlify-cli"
fi 