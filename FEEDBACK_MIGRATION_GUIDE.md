# Feedback System Database Migration Guide

## The Issue
You're getting a 500 error because the `Feedback` table doesn't exist in your database yet. The new Feedback entity needs to be migrated to the database.

## Solution Steps

### 1. Run Database Migration

You need to generate and apply a migration for the new Feedback entity:

```bash
# Option 1: Using Wasp CLI (if installed)
wasp db migrate-dev

# Option 2: Using npm (if Wasp CLI not available)
npm run db:migrate-dev

# Option 3: Using npx
npx wasp db migrate-dev
```

### 2. If Migration Fails

If the migration command doesn't work, you can manually create the table:

```sql
-- Connect to your PostgreSQL database and run this SQL:

CREATE TABLE "Feedback" (
    "id" TEXT NOT NULL,
    "userId" INTEGER,
    "guestEmail" TEXT,
    "type" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "screenshot" TEXT,
    "url" TEXT,
    "userAgent" TEXT,
    "status" TEXT NOT NULL DEFAULT 'new',
    "adminNotes" TEXT,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "assignedTo" TEXT,
    "resolvedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Feedback_pkey" PRIMARY KEY ("id")
);

-- Create indexes for better performance
CREATE INDEX "Feedback_status_createdAt_idx" ON "Feedback"("status", "createdAt");
CREATE INDEX "Feedback_type_createdAt_idx" ON "Feedback"("type", "createdAt");
CREATE INDEX "Feedback_userId_createdAt_idx" ON "Feedback"("userId", "createdAt");
CREATE INDEX "Feedback_createdAt_idx" ON "Feedback"("createdAt");

-- Add foreign key constraint
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
```

### 3. Verify Database Connection

Make sure your database is running and accessible:

```bash
# Check if PostgreSQL is running
pg_isready -h localhost -p 5432

# Or check your connection string in .env.server
echo $DATABASE_URL
```

### 4. Test the Feedback System

After migration:

1. **Submit test feedback** using the feedback widget
2. **Check admin dashboard** at `/admin/feedback`
3. **Verify data** is being stored correctly

## Expected Database Schema

The Feedback table should have these columns:

| Column | Type | Description |
|--------|------|-------------|
| id | TEXT (UUID) | Primary key |
| userId | INTEGER | Foreign key to User (nullable) |
| guestEmail | TEXT | Email for guest users (nullable) |
| type | TEXT | bug, feature, improvement, general, other |
| description | TEXT | Feedback content |
| screenshot | TEXT | Base64 encoded image (nullable) |
| url | TEXT | Page where feedback was submitted |
| userAgent | TEXT | Browser/device info |
| status | TEXT | new, in_progress, resolved, closed |
| adminNotes | TEXT | Admin comments (nullable) |
| priority | TEXT | low, medium, high, urgent |
| assignedTo | TEXT | Admin handling the feedback (nullable) |
| resolvedAt | TIMESTAMP | When feedback was resolved (nullable) |
| createdAt | TIMESTAMP | When feedback was created |
| updatedAt | TIMESTAMP | Last update time |

## Troubleshooting

### Error: "relation 'Feedback' does not exist"
- Run the migration command above
- Or manually create the table using the SQL provided

### Error: "permission denied"
- Check database user permissions
- Ensure your DATABASE_URL has correct credentials

### Error: "database does not exist"
- Create the database first: `createdb your_database_name`
- Or check your DATABASE_URL

### Still Getting 500 Errors?
1. Check server logs for detailed error messages
2. Verify the Feedback entity is in schema.prisma
3. Restart the Wasp development server
4. Check that all imports are correct in main.wasp

## Testing After Migration

1. **Submit feedback** via the widget
2. **Check server logs** for success messages
3. **Visit admin dashboard** at `/admin/feedback`
4. **Verify email notifications** are sent

The feedback system should work perfectly once the database migration is complete!
