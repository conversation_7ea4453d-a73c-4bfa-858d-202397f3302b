app CareerDart {
  wasp: {
    version: "^0.15.0"
  },
  // Force rebuild to pick up tsconfig.json changes
  title: "CareerDart",
  head: [
        "<meta charset=\"UTF-8\" />",
        "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />",
        "<meta name=\"description\" content=\"Launch Your Career - Rocket-powered AI tools for creating stunning resumes, cover letters, and mastering interviews to accelerate your professional journey.\" />",
        "<meta name=\"keywords\" content=\"career, resume, cover letter, ai, job search, interview prep, career target, precision\" />",
        "<meta name=\"author\" content=\"CareerDart\" />",
        "<link rel=\"icon\" type=\"image/x-icon\" href=\"/favicon.ico\" />",
        "<link rel=\"shortcut icon\" href=\"/favicon.ico\" />",
        "<link rel=\"icon\" type=\"image/svg+xml\" href=\"/images/careerdart-icon.svg\" />",
        "<link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\" />",
        "<link rel=\"manifest\" href=\"/manifest.json\" />",
        "<meta name=\"theme-color\" content=\"#3182ce\" />",
        "<meta property=\"og:type\" content=\"website\" />",
        "<meta property=\"og:url\" content=\"https://CareerDart.com\" />",
        "<meta property=\"og:title\" content=\"CareerDart - Launch Your Career\" />",
        "<meta property=\"og:description\" content=\"Launch Your Career - Rocket-powered AI tools for creating stunning resumes, cover letters, and mastering interviews to accelerate your professional journey.\" />",
        "<meta property=\"og:image\" content=\"https://CareerDart.com/homepage.png\" />",
        "<meta name=\"twitter:image\" content=\"https://CareerDart.com/homepage.png\" />",
        "<meta name=\"twitter:image:width\" content=\"800\" />",
        "<meta name=\"twitter:image:height\" content=\"400\" />",
        "<meta name=\"twitter:card\" content=\"summary_large_image\" />",
        "<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />",
        "<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin />",
        "<link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\" />",
        "<script>try{var d=document.documentElement,c=d.classList;c.remove('light','dark');var e=localStorage.getItem('chakra-ui-color-mode');if('dark'===e||(!e&&window.matchMedia('(prefers-color-scheme: dark)').matches)){c.add('dark')}else{c.add('light')}}catch(e){}</script>",
  ],
  // 🔐 Auth out of the box! https://wasp-lang.dev/docs/language/features#authentication--authorization
  auth: {
    userEntity: User,
    methods: {
      email: {
        userSignupFields: import { getUserFields } from "@src/server/auth.ts",
        fromField: {
          name: "CareerDart",
          email: "<EMAIL>"
        },
        emailVerification: {
          clientRoute: EmailVerificationRoute,
          getEmailContentFn: import { getVerificationEmailContent } from "@src/server/auth.ts",
        },
        passwordReset: {
          clientRoute: PasswordResetRoute,
          getEmailContentFn: import { getPasswordResetEmailContent } from "@src/server/auth.ts",
        },
      },
      google: {
        userSignupFields: import { getUserFields } from "@src/server/auth.ts",
        configFn: import { config } from "@src/server/auth.ts",
      },
    },
    onAuthFailedRedirectTo: "/login",
    onAuthSucceededRedirectTo: "/dashboard",
  },
  client: {
    rootComponent: import App from "@src/client/App",
    setupFn: import clientSetup from "@src/client/clientSetup"
  },
  emailSender: {
    provider: SMTP,
    defaultFrom: {
      name: "CareerDart",
      email: "<EMAIL>",
    },
  },
  server: {
    middlewareConfigFn: import { customMiddlewareConfig } from "@src/server/middleware.js"
  },
}

/* 📡 These are the Wasp Routes (You can protect them easily w/ 'authRequired: true');
 * https://wasp-lang.dev/docs/language/features#route
 */

route RootRoute { path: "/", to: MainPage }
page MainPage {
  component: import Main from "@src/client/MainPage"
}

route DashboardRoute { path: "/dashboard", to: DashboardPage }
page DashboardPage {
  authRequired: true,
  component: import Dashboard from "@src/client/DashboardPage"
}

route CoverLettersRoute { path: "/cover-letters", to: CoverLettersPage }
page CoverLettersPage {
  authRequired: true,
  component: import CoverLetters from "@src/client/CoverLettersPage"
}

route CoverLetterPage { path: "/cover-letters/:id", to: CoverLetterPage }
page CoverLetterPage {
  authRequired: true,
  component: import CoverLetter from "@src/client/CoverLetterPage"
}

route LoginRoute { path: "/login", to: LoginPage }
page LoginPage {
  component: import Login from "@src/client/LoginPage"
}

route EmailVerificationRoute { path: "/email-verification", to: EmailVerificationPage }
page EmailVerificationPage {
  component: import EmailVerification from "@src/client/EmailVerificationPage"
}

route PasswordResetRoute { path: "/password-reset", to: PasswordResetPage }
page PasswordResetPage {
  component: import PasswordReset from "@src/client/PasswordResetPage"
}

route JobsRoute { path: "/jobs", to: JobsPage }
page JobsPage {
  authRequired: true,
  component: import Jobs from "@src/client/JobsPage"
}

route ProfileRoute { path: "/profile", to: ProfilePage }
page ProfilePage {
  authRequired: true,
  component: import Profile from "@src/client/ProfilePage"
}

route CheckoutRoute { path: "/checkout", to: CheckoutPage }
page CheckoutPage {
  authRequired: true,
  component: import Checkout from "@src/client/CheckoutPage"
}

route TosRoute { path: "/tos", to: TosPage }
page TosPage {
  component: import Tos from "@src/client/legal/TosPage"
}

route PrivacyRoute { path: "/privacy", to: PrivacyPage }
page PrivacyPage {
  component: import Privacy from "@src/client/legal/PrivacyPolicyPage"
}

route ResumeRoute { path: "/resume", to: ResumePage }
page ResumePage {
  authRequired: true,
  component: import Resume from "@src/client/ResumePage"
}

route InterviewRoute { path: "/interview", to: InterviewPrepPage }
page InterviewPrepPage {
  authRequired: true,
  component: import InterviewPrep from "@src/client/InterviewPrepPage"
}

route LearningRoute { path: "/learning", to: LearningPage }
page LearningPage {
  authRequired: true,
  component: import Learning from "@src/client/LearningPage"
}

route TrackerRoute { path: "/tracker", to: TrackerPage }
page TrackerPage {
  authRequired: true,
  component: import Tracker from "@src/client/TrackerPage"
}

route HelpRoute { path: "/help", to: HelpPage }
page HelpPage {
  component: import Help from "@src/client/HelpPage"
}

route AdminRoute { path: "/admin", to: AdminDashboardPage }
page AdminDashboardPage {
  authRequired: true,
  component: import AdminDashboard from "@src/client/AdminDashboardPage"
}

route NotFoundRoute { path: "*", to: NotFoundPage }
page NotFoundPage {
  component: import NotFound from "@src/client/NotFoundPage"
}

/* ⛑ These are the Wasp Operations, which allow the client and server to interact:
 * https://wasp-lang.dev/docs/language/features#queries-and-actions-aka-operations
 */

// 📝 Actions aka Mutations

action generateCoverLetter {
  fn: import { generateCoverLetter } from "@src/server/actions.js",
  entities: [CoverLetter, User]
}

action createJob {
  fn: import { createJob } from "@src/server/actions.js",
  entities: [Job]
}

action updateJob {
  fn: import { updateJob } from "@src/server/actions.js",
  entities: [Job]
}

action importJobFromLinkedIn {
  fn: import { importJobFromLinkedIn } from "@src/server/actions.js",
  entities: [Job, User]
}

action importJobFromUrl {
  fn: import { importJobFromUrl } from "@src/server/actions.js",
  entities: [Job, User]
}

action updateCoverLetter {
  fn: import { updateCoverLetter } from "@src/server/actions.js",
  entities: [Job, CoverLetter, User]
}

action generateEdit {
  fn: import { generateEdit } from "@src/server/actions.js",
  entities: [CoverLetter, User]
}

action editCoverLetter {
  fn: import { editCoverLetter } from "@src/server/actions.js",
  entities: [CoverLetter]
}

action updateUser {
  fn: import { updateUser } from "@src/server/actions.js",
  entities: [User]
}

action adminDeleteUserAccount {
  fn: import { adminDeleteUserAccount } from "@src/server/admin.js",
  entities: [User, UserActivity]
}

action deleteUserAccount {
  fn: import { deleteUserAccount } from "@src/server/actions.js",
  entities: [User]
}

action deleteJob {
  fn: import { deleteJob } from "@src/server/actions.js",
  entities: [Job]
}

action stripePayment {
  fn: import { stripePayment } from "@src/server/actions.js",
  entities: [User]
}

action stripeGpt4Payment {
  fn: import { stripeGpt4Payment } from "@src/server/actions.js",
  entities: [User]
}

action stripeCreditsPayment {
  fn: import { stripeCreditsPayment } from "@src/server/actions.js",
  entities: [User]
}

action testSMTPEmail {
  fn: import { testResendSMTP } from "@src/server/testEmail.js",
  entities: []
}

action sendResendEmail {
  fn: import { sendResendEmail } from "@src/server/resendEmail.js",
  entities: []
}

action sendVerificationEmailResend {
  fn: import { sendVerificationEmailResend } from "@src/server/resendEmail.js",
  entities: []
}

action sendPasswordResetEmailResend {
  fn: import { sendPasswordResetEmailResend } from "@src/server/resendEmail.js",
  entities: []
}

action createResume {
  fn: import { createResume } from "@src/server/operations.js",
  entities: [Resume, User]
}

action updateResume {
  fn: import { updateResume } from "@src/server/operations.js",
  entities: [Resume, User]
}

action deleteResume {
  fn: import { deleteResume } from "@src/server/operations.js",
  entities: [Resume, User]
}

action parsePDFContent {
  fn: import { parsePDFContent } from "@src/server/operations.js",
  entities: [User]
}

action generateInterviewQuestions {
  fn: import { generateInterviewQuestions } from "@src/server/operations.js",
  entities: [Job, User, InterviewQuestionSet]
}

action saveInterviewQuestion {
  fn: import { saveInterviewQuestion } from "@src/server/operations.js",
  entities: [SavedInterviewQuestion, User]
}

action removeSavedInterviewQuestion {
  fn: import { removeSavedInterviewQuestion } from "@src/server/operations.js",
  entities: [SavedInterviewQuestion, User]
}

action savePracticeAnswer {
  fn: import { savePracticeAnswer } from "@src/server/operations.js",
  entities: [PracticeAnswer, User]
}

action deletePracticeAnswer {
  fn: import { deletePracticeAnswer } from "@src/server/operations.js",
  entities: [PracticeAnswer, User]
}

action createJobApplication {
  fn: import { createJobApplication } from "@src/server/operations.js",
  entities: [JobApplication, Job, User]
}

action updateJobApplication {
  fn: import { updateJobApplication } from "@src/server/operations.js",
  entities: [JobApplication, Job, User]
}

action deleteJobApplication {
  fn: import { deleteJobApplication } from "@src/server/operations.js",
  entities: [JobApplication, Job, User]
}

action updateLearningProgress {
  fn: import { updateLearningProgress } from "@src/server/actions.js",
  entities: [LearningProgress, User, Badge, UserBadge]
}

action initializeBadges {
  fn: import { initializeBadges } from "@src/server/actions.js",
  entities: [Badge]
}

action reportPerformanceMetrics {
  fn: import { reportPerformanceMetrics } from "@src/server/actions.js",
  entities: [User]
}

action checkEmailVerificationStatus {
  fn: import { checkEmailVerificationStatus } from "@src/server/actions.js",
  entities: [User]
}

query getUserResumes {
  fn: import { getUserResumes } from "@src/server/operations.js",
  entities: [Resume, User]
}

// 📚 Queries

query getJobs {
  fn: import { getJobs } from "@src/server/queries.js",
  entities: [Job]
}

query getJob {
  fn: import { getJob } from "@src/server/queries.js",
  entities: [Job]
}

query getCoverLetter {
  fn: import { getCoverLetter } from "@src/server/queries.js",
  entities: [CoverLetter]
}

query getCoverLetters {
  fn: import { getCoverLetters } from "@src/server/queries.js",
  entities: [CoverLetter]
}

query getUserInfo {
  fn: import { getUserInfo } from "@src/server/queries.js",
  entities: [User]
}

query getUserPreferences {
  fn: import { getUserPreferences } from "@src/server/queries.js",
  entities: [User]
}

query getCoverLetterCount {
  fn: import { getCoverLetterCount } from "@src/server/queries.js",
  entities: [CoverLetter]
}

query getAllCoverLetters {
  fn: import { getAllCoverLetters } from "@src/server/queries.js",
  entities: [CoverLetter]
}

query getJobApplications {
  fn: import { getJobApplications } from "@src/server/queries.js",
  entities: [JobApplication, Job, User]
}

query getLearningProgress {
  fn: import { getLearningProgress } from "@src/server/queries.js",
  entities: [LearningProgress, User]
}

query getUserBadges {
  fn: import { getUserBadges } from "@src/server/queries.js",
  entities: [UserBadge, Badge, User]
}

query getAllBadges {
  fn: import { getAllBadges } from "@src/server/queries.js",
  entities: [Badge]
}

query getInterviewQuestionSets {
  fn: import { getInterviewQuestionSets } from "@src/server/operations.js",
  entities: [InterviewQuestionSet, User]
}

query getSavedInterviewQuestions {
  fn: import { getSavedInterviewQuestions } from "@src/server/operations.js",
  entities: [SavedInterviewQuestion, User]
}

query getPracticeAnswers {
  fn: import { getPracticeAnswers } from "@src/server/operations.js",
  entities: [PracticeAnswer, User]
}

// Admin Dashboard Operations
query getAdminAnalytics {
  fn: import { getAdminAnalytics } from "@src/server/admin.js",
  entities: [User, CoverLetter, Job, Resume, SystemMetrics, UserActivity, ErrorLog, FeatureUsage]
}

query getSystemMetrics {
  fn: import { getSystemMetrics } from "@src/server/admin.js",
  entities: [SystemMetrics]
}

query getUserActivity {
  fn: import { getUserActivity } from "@src/server/admin.js",
  entities: [UserActivity, User]
}

query getErrorLogs {
  fn: import { getErrorLogs } from "@src/server/admin.js",
  entities: [ErrorLog]
}

query getAllUsers {
  fn: import { getAllUsers } from "@src/server/admin.js",
  entities: [User]
}

action logUserActivity {
  fn: import { logUserActivity } from "@src/server/admin.js",
  entities: [UserActivity]
}

action logErrorEvent {
  fn: import { logErrorEvent } from "@src/server/admin.js",
  entities: [ErrorLog]
}

action recordSystemMetric {
  fn: import { recordSystemMetric } from "@src/server/admin.js",
  entities: [SystemMetrics]
}

action updateUserRole {
  fn: import { updateUserRole } from "@src/server/admin.js",
  entities: [User, AdminAuditLog]
}

query getLighthouseMetrics {
  fn: import { getLighthouseMetrics } from "@src/server/admin.js",
  entities: []
}

// Admin User Management Operations
action updateUserDetails {
  fn: import { updateUserDetails } from "@src/server/admin.js",
  entities: [User, UserActivity]
}

action setUserVerified {
  fn: import { setUserVerified } from "@src/server/admin.js",
  entities: [User, UserActivity]
}

action sendUserEmail {
  fn: import { sendUserEmail } from "@src/server/admin.js",
  entities: [User, UserActivity]
}

/* 📡 These are custom Wasp API Endpoints. Use them for callbacks, webhooks, etc.
 * https://wasp-lang.dev/docs/language/features#apis
 */

// Health check endpoint for fly.io and monitoring
api healthCheck {
  fn: import { healthCheck } from "@src/server/health.js",
  httpRoute: (GET, "/health")
}

api stripeWebhook {
  fn: import { stripeWebhook } from "@src/server/webhooks.js",
  entities: [User],
  httpRoute: (POST, "/stripe-webhook")
}

// Jobs API
api jobsApi {
  fn: import { jobsApi } from "@src/server/api.js",
  httpRoute: (POST, "/api/jobs")
}

api importJobFromUrlApi {
  fn: import { importJobFromUrlApi } from "@src/server/api.js",
  httpRoute: (POST, "/api/import-job-from-url")
}

/* 🕵️‍♂️ These are the Wasp Cron Jobs. Use them to set up recurring tasks or queues:
 * https://wasp-lang.dev/docs/language/features#jobs
 */

job checkUserSubscription {
  executor: PgBoss,
  perform: {
    fn: import { updateUserSubscription } from "@src/server/workers/updateUserSubscription.js"
  },
  schedule: {
    cron: "0 23 * * *"
  },
  entities: [User]
}

action fetchRecommendedJobs {
  fn: import { fetchRecommendedJobs } from "@src/server/actions.js",
  entities: [User, Job]
}

action fetchAvailableJobs {
  fn: import { fetchAvailableJobs } from "@src/server/actions.js",
  entities: [User, Job]
}

action submitFeedback {
  fn: import { submitFeedback } from "@src/server/feedback.js",
  entities: [User]
}
