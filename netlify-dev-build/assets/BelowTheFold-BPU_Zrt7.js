const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DzSDmceP.js","assets/ui-d9gyv7ZA.js","assets/vendor-BJbfb-22.js","assets/icons-wvpQ6TvE.js","assets/utils-wRZoJTGj.js","assets/index-Vbu6rsP6.css"])))=>i.map(i=>d[i]);
import{N as e,O as r,P as o,Q as s,R as n,S as i,r as t,c as a,f as l,e as c,h as d,g as p,J as x,U as h,_ as g}from"./index-DzSDmceP.js";import{l as m,j as u,N as j,V as f,T as y,a as b,F as v,h as z,t as S,H as w,B as A,L as C}from"./ui-d9gyv7ZA.js";import{b as W,d as k}from"./vendor-BJbfb-22.js";import"./icons-wvpQ6TvE.js";import"./utils-wRZoJTGj.js";const O=k.lazy((()=>g((()=>import("./index-DzSDmceP.js").then((e=>e.V))),__vite__mapDeps([0,1,2,3,4,5])))),_=[{icon:t,title:"AI-Powered Resume Builder",description:"Create professional resumes in minutes with our intelligent AI that adapts to your industry and role.",color:"blue.500"},{icon:a,title:"Smart Cover Letters",description:"Generate personalized cover letters that perfectly match job descriptions and showcase your strengths.",color:"purple.500"},{icon:l,title:"Interview Preparation",description:"Practice with AI-generated questions tailored to your target role and get instant feedback.",color:"green.500"},{icon:c,title:"Job Application Tracker",description:"Organize your job search with our comprehensive tracking system and never miss an opportunity.",color:"orange.500"},{icon:d,title:"Career Analytics",description:"Get insights into your job search performance and optimize your strategy for better results.",color:"teal.500"},{icon:p,title:"Learning Center",description:"Access curated career development resources and courses to advance your professional skills.",color:"pink.500"}],I=[{name:"Google",icon:e},{name:"Microsoft",icon:r},{name:"Apple",icon:o},{name:"Amazon",icon:s},{name:"Facebook",icon:n},{name:"Dropbox",icon:i}],Y=({user:e,setShowCoverLetterForm:r,navigate:o})=>{const s=m("white","gray.800"),n=m("gray.700","gray.200");return u.jsxs(u.Fragment,{children:[u.jsx(j,{maxW:"95%",py:{base:8,md:12},children:u.jsxs(f,{spacing:6,children:[u.jsx(y,{fontSize:"lg",fontWeight:"600",color:"gray.500",textAlign:"center",children:"Trusted by job seekers who've landed at top companies"}),u.jsx(y,{fontSize:"sm",color:"gray.400",textAlign:"center",children:"Our users have secured positions at industry-leading companies such as"}),u.jsx(b,{w:"full",overflow:"hidden",children:u.jsx(v,{animation:"scroll 30s linear infinite",sx:{"@keyframes scroll":{"0%":{transform:"translateX(0)"},"100%":{transform:"translateX(-50%)"}}},children:[...I,...I].map(((e,r)=>u.jsx(v,{minW:"200px",h:"80px",align:"center",justify:"center",mx:4,children:u.jsxs(z,{spacing:3,opacity:.6,_hover:{opacity:1},transition:"opacity 0.3s",children:[u.jsx(S,{as:e.icon,boxSize:8,color:"gray.500"}),u.jsx(y,{fontSize:"lg",fontWeight:"600",color:"gray.500",children:e.name})]})},r)))})})]})}),u.jsx(j,{maxW:"95%",py:{base:10,md:16},children:u.jsxs(f,{spacing:12,children:[u.jsxs(f,{spacing:4,textAlign:"center",children:[u.jsx(w,{size:"2xl",fontWeight:"black",color:n,children:"Intelligent Career Development That Delivers"}),u.jsx(y,{fontSize:"xl",color:"gray.500",maxW:"3xl",children:"Our personalized AI approach creates measurable results for professionals across all industries"})]}),u.jsx(A,{size:"lg",colorScheme:"blue",px:8,py:6,fontSize:"lg",fontWeight:"600",borderRadius:"xl",onClick:()=>e?r(!0):o("/login"),_hover:{transform:"translateY(-2px)",boxShadow:"xl"},transition:"all 0.3s",children:e?"Start Your Journey":"Start for FREE"})]})}),u.jsx(j,{maxW:"95%",py:{base:10,md:16},children:u.jsxs(f,{spacing:12,children:[u.jsxs(f,{spacing:4,textAlign:"center",children:[u.jsx(w,{size:"2xl",fontWeight:"black",color:n,children:"Everything You Need to Land Your Dream Job"}),u.jsx(y,{fontSize:"xl",color:"gray.500",maxW:"3xl",children:"Our comprehensive suite of AI-powered tools gives you the competitive edge in today's job market."})]}),u.jsx(C,{templateColumns:{base:"1fr",md:"repeat(2, 1fr)",lg:"repeat(3, 1fr)"},gap:8,children:_.map(((e,r)=>u.jsx(b,{bg:s,borderRadius:"2xl",p:8,border:"1px solid",borderColor:m("gray.200","gray.700"),_hover:{transform:"translateY(-4px)",boxShadow:"xl"},transition:"all 0.3s",children:u.jsxs(f,{spacing:4,align:"start",children:[u.jsx(S,{as:e.icon,boxSize:10,color:e.color}),u.jsx(w,{size:"md",fontWeight:"bold",color:n,children:e.title}),u.jsx(y,{color:"gray.500",lineHeight:"tall",children:e.description})]})},r)))})]})}),u.jsx(j,{maxW:"95%",py:{base:10,md:16},children:u.jsx(f,{spacing:12,children:u.jsxs(f,{spacing:8,textAlign:"center",maxW:"3xl",mx:"auto",children:[u.jsx(w,{size:"2xl",fontWeight:"black",color:n,children:"Ready to Transform Your Career?"}),u.jsx(y,{fontSize:"xl",color:"gray.500",children:"Join thousands of professionals who are already using AI-powered tools to advance their careers"}),u.jsx(A,{size:"lg",colorScheme:"blue",px:12,py:8,fontSize:"xl",fontWeight:"700",borderRadius:"2xl",onClick:()=>e?r(!0):o("/login"),_hover:{transform:"translateY(-3px)",boxShadow:"0 20px 40px rgba(66, 153, 225, 0.4)"},transition:"all 0.3s",children:e?"Start Your Journey":"Begin Your Career Transformation"})]})})}),u.jsx(x,{isOpen:!1,onOpen:()=>{},onClose:()=>{},credits:e?.credits||0}),u.jsx(h,{isOpen:!1,onOpen:()=>{},onClose:()=>{}}),u.jsx(W.Suspense,{fallback:u.jsx(u.Fragment,{}),children:u.jsx(O,{isOpen:!1,onClose:()=>{},userCredits:e?.credits||0,feature:"cover-letter"})})]})};export{Y as default};
