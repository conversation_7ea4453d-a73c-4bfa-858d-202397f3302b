import{u as e,j as s,V as n,a as o,T as t,G as i,B as l}from"./ui-CxDIvMgK.js";import{T as r,H as a,I as c,J as d,K as x}from"./index-Cd7NcsC-.js";import{b as m}from"./vendor-BJbfb-22.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";function g({setTooltip:g,selectedText:h,user:p,...f}){const{textareaState:j,setTextareaState:S}=m.useContext(r),{data:u}=a(c,{id:p.id}),{isOpen:v,onOpen:k,onClose:z}=e(),b=async e=>{if(!u?.credits&&!u?.hasPaid)return k(),g(null),void window.getSelection()?.removeAllRanges();(async({improvement:e})=>{const s=window.getSelection();let n;try{const o=j,t=s.toString(),i=o.indexOf(t);let l="Loading";n=setInterval((()=>{if(l.length<10){l+=".";let e=o.slice(0,i+t.length)+"\n --- \n"+l+"\n --- \n"+o.slice(i+t.length);S(e)}else l="Loading"}),750);const r=await x({content:t,improvement:e});clearInterval(n),S(o);const a=o.slice(0,i+t.length)+"\n --- Revision: \n"+r+"\n --- \n"+o.slice(i+t.length);S(a)}catch(o){clearInterval(n),alert(o?.message??"An error has occurred")}})({improvement:e}),window.getSelection()?.removeAllRanges()};return s.jsxs(s.Fragment,{children:[s.jsx(n,{...f,gap:1,bgColor:"bg-modal",borderRadius:"lg",boxShadow:"2xl",children:s.jsxs(o,{layerStyle:"cardLg",p:3,children:[s.jsx(t,{fontSize:"sm",textAlign:"center",children:"🤔 Ask GPT to make this part more.."}),s.jsxs(i,{size:"xs",p:1,variant:"solid",colorScheme:"purple",isAttached:!0,children:[s.jsx(l,{size:"xs",color:"black",fontSize:"xs",onClick:()=>b("concise"),children:"Concise"}),s.jsx(l,{size:"xs",color:"black",fontSize:"xs",onClick:()=>b("detailed"),children:"Detailed"}),s.jsx(l,{size:"xs",color:"black",fontSize:"xs",onClick:()=>b("Professional"),children:"Professional"}),s.jsx(l,{size:"xs",color:"black",fontSize:"xs",onClick:()=>b("informal"),children:"Informal"})]})]})}),s.jsx(d,{isOpen:v,onOpen:k,onClose:z,credits:u?.credits||0})]})}export{g as EditPopover};
