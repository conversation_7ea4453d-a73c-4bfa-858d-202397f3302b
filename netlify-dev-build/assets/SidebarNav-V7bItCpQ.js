import{k as e,a1 as a,j as s,aT as i,l,aU as r,o as t,n as o,p as n,a as d,V as m,y as c,I as p,Q as b,T as x,K as j}from"./ui-CxDIvMgK.js";import{u as h,a as u,b as g,F as f,c as y,d as v,e as k,f as z,g as C,h as w,i as S,j as I,k as R,l as T}from"./index-Cd7NcsC-.js";import{useSidebar as W}from"./NavBar-C1WeduWJ.js";import"./vendor-BJbfb-22.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";import"./Logo-C3fvvPlr.js";const A=[{label:"Dashboard",icon:f,path:"/dashboard"}],F=[{label:"Cover Letters",icon:y,path:"/cover-letters"},{label:"Resume",icon:v,path:"/resume"},{label:"Jobs",icon:k,path:"/jobs"}],L=[{label:"Interview Prep",icon:z,path:"/interview"},{label:"Learning",icon:C,path:"/learning"},{label:"Tracker",icon:w,path:"/tracker"}],M=[{label:"Profile",icon:I,path:"/profile"}],_=[{label:"Admin Dashboard",icon:R,path:"/admin"}];function B(){const f=e("white","gray.900"),y=e("blue.50","gray.700"),v=e("gray.700","gray.200"),k=e("gray.200","gray.700"),{data:z}=h(),C=u(),w=g(),{isSidebarOpen:I,closeSidebar:R,isCollapsed:B,toggleCollapse:D}=W(),E=a({base:!0,md:!1}),O=z?.email?.includes("admin")||z?.email?.endsWith("@admin.careerdart.com"),P=({item:a})=>{const i=w.pathname===a.path,l=e("blue.50","blue.900"),r=e("blue.600","blue.300");return s.jsxs(d,{as:"button",onClick:()=>{return e=a.path,void(z||"/"===e?(C(e),E&&R()):C("/login"));var e},display:"flex",alignItems:"center",padding:"0.5rem",borderRadius:"0.375rem",color:i?r:v,bg:i?l:"transparent",width:"100%",textAlign:"left",border:"none",cursor:"pointer",_hover:{bg:i?l:y},transition:"all 0.2s",title:B?a.label:void 0,children:[s.jsx(j,{as:a.icon,mr:B?0:3,color:"blue.500",boxSize:5}),!B&&s.jsx(x,{fontWeight:i?"semibold":"medium",children:a.label})]},a.label)},U=({title:e})=>B?s.jsx(b,{my:2}):s.jsxs(c,{justify:"space-between",align:"center",mb:1,mt:3,px:1,children:[s.jsx(x,{fontSize:"xs",fontWeight:"medium",textTransform:"uppercase",color:"gray.500",_dark:{color:"gray.400"},letterSpacing:"wider",children:e}),"Main"===e&&s.jsx(p,{"aria-label":B?"Expand sidebar":"Collapse sidebar",icon:s.jsx(T,{size:"10px"}),size:"xs",onClick:D,variant:"ghost",colorScheme:"blue",borderRadius:"full"})]}),J=()=>s.jsxs(m,{align:"stretch",spacing:2,mt:2,children:[B&&s.jsx(c,{justify:"center",mb:3,children:s.jsx(p,{"aria-label":"Expand sidebar",icon:s.jsx(S,{size:"12px"}),size:"sm",onClick:D,variant:"ghost",colorScheme:"blue",borderRadius:"full"})}),s.jsx(U,{title:"Main"}),A.map((e=>s.jsx(P,{item:e},e.label))),s.jsx(b,{my:2}),s.jsx(U,{title:"Content"}),F.map((e=>s.jsx(P,{item:e},e.label))),s.jsx(b,{my:2}),s.jsx(U,{title:"Tools & Resources"}),L.map((e=>s.jsx(P,{item:e},e.label))),z&&s.jsxs(s.Fragment,{children:[s.jsx(b,{my:2}),s.jsx(U,{title:"User"}),M.map((e=>s.jsx(P,{item:e},e.label)))]}),z&&O&&s.jsxs(s.Fragment,{children:[s.jsx(b,{my:2}),s.jsx(U,{title:"Admin"}),_.map((e=>s.jsx(P,{item:e},e.label)))]})]});return E?s.jsxs(i,{isOpen:I,placement:"left",onClose:R,size:"xs",children:[s.jsx(l,{}),s.jsxs(r,{bg:f,zIndex:4,children:[s.jsx(t,{}),s.jsx(o,{borderBottomWidth:"1px",borderColor:k,py:3,px:4,fontSize:"md",fontWeight:"semibold",color:"blue.600",_dark:{color:"blue.300"},children:"Menu"}),s.jsx(n,{px:3,py:2,children:s.jsx(J,{})})]})]}):s.jsx(d,{as:"nav",w:B?"60px":"220px",bg:f,h:"100vh",p:B?2:4,boxShadow:"md",position:"fixed",top:"0",left:"0",zIndex:"5",pt:"80px",display:{base:"none",md:"/"!==w.pathname?"block":"none"},transition:"width 0.3s ease, padding 0.3s ease",children:s.jsx(J,{})})}export{B as default};
