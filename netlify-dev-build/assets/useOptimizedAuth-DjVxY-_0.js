import"./ui-CxDIvMgK.js";import{u as t}from"./index-C1qB3iHx.js";import{b as e}from"./vendor-BJbfb-22.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";let o=null,n=0;const s=console.error;function a(){const s=t(),[a,i]=e.useState((()=>o||s)),r=e.useRef(n);return e.useEffect((()=>{const t=Date.now();(t-r.current>3e4||s.data?.id!==a.data?.id||s.isLoading!==a.isLoading||s.data&&!a.data||!s.data&&a.data)&&(i(s),r.current=t,o=s,n=t)}),[s,a]),e.useMemo((()=>a),[a])}function i(){const[o,n]=e.useState(null),[s,a]=e.useState(!1),i=t();return e.useEffect((()=>{s||i.isLoading||(n(i),a(!0))}),[i,s]),s?o:i}console.error=(...t)=>{const e=t.join(" ");e.includes("401")&&(e.includes("/auth/me")||e.includes("Unauthorized"))||s.apply(console,t)};export{a as useOptimizedAuth,i as useStaticAuth};
