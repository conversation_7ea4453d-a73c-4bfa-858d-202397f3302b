const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-jv3y70DG.js","assets/ui-CxDIvMgK.js","assets/vendor-BJbfb-22.js","assets/icons-DRFAkztb.js","assets/utils-DuWhH-qE.js","assets/index-Vbu6rsP6.css"])))=>i.map(i=>d[i]);
import{N as e,O as r,P as s,Q as o,R as a,S as n,r as i,c as t,f as l,e as c,h as d,g as p,J as x,U as h,_ as m}from"./index-jv3y70DG.js";import{k as g,j as u,X as f,V as j,T as y,a as b,y as v,h as z,K as S,H as w,B as A,W}from"./ui-CxDIvMgK.js";import{b as k,d as C}from"./vendor-BJbfb-22.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";const O=C.lazy((()=>m((()=>import("./index-jv3y70DG.js").then((e=>e.V))),__vite__mapDeps([0,1,2,3,4,5])))),_=[{icon:i,title:"AI-Powered Resume Builder",description:"Create professional resumes in minutes with our intelligent AI that adapts to your industry and role.",color:"blue.500"},{icon:t,title:"Smart Cover Letters",description:"Generate personalized cover letters that perfectly match job descriptions and showcase your strengths.",color:"purple.500"},{icon:l,title:"Interview Preparation",description:"Practice with AI-generated questions tailored to your target role and get instant feedback.",color:"green.500"},{icon:c,title:"Job Application Tracker",description:"Organize your job search with our comprehensive tracking system and never miss an opportunity.",color:"orange.500"},{icon:d,title:"Career Analytics",description:"Get insights into your job search performance and optimize your strategy for better results.",color:"teal.500"},{icon:p,title:"Learning Center",description:"Access curated career development resources and courses to advance your professional skills.",color:"pink.500"}],I=[{name:"Google",icon:e},{name:"Microsoft",icon:r},{name:"Apple",icon:s},{name:"Amazon",icon:o},{name:"Facebook",icon:a},{name:"Dropbox",icon:n}],Y=({user:e,setShowCoverLetterForm:r,navigate:s})=>{const o=g("white","gray.800"),a=g("gray.700","gray.200");return u.jsxs(u.Fragment,{children:[u.jsx(f,{maxW:"95%",py:{base:8,md:12},children:u.jsxs(j,{spacing:6,children:[u.jsx(y,{fontSize:"lg",fontWeight:"600",color:"gray.500",textAlign:"center",children:"Trusted by job seekers who've landed at top companies"}),u.jsx(y,{fontSize:"sm",color:"gray.400",textAlign:"center",children:"Our users have secured positions at industry-leading companies such as"}),u.jsx(b,{w:"full",overflow:"hidden",children:u.jsx(v,{animation:"scroll 30s linear infinite",sx:{"@keyframes scroll":{"0%":{transform:"translateX(0)"},"100%":{transform:"translateX(-50%)"}}},children:[...I,...I].map(((e,r)=>u.jsx(v,{minW:"200px",h:"80px",align:"center",justify:"center",mx:4,children:u.jsxs(z,{spacing:3,opacity:.6,_hover:{opacity:1},transition:"opacity 0.3s",children:[u.jsx(S,{as:e.icon,boxSize:8,color:"gray.500"}),u.jsx(y,{fontSize:"lg",fontWeight:"600",color:"gray.500",children:e.name})]})},r)))})})]})}),u.jsx(f,{maxW:"95%",py:{base:10,md:16},children:u.jsxs(j,{spacing:12,children:[u.jsxs(j,{spacing:4,textAlign:"center",children:[u.jsx(w,{size:"2xl",fontWeight:"black",color:a,children:"Intelligent Career Development That Delivers"}),u.jsx(y,{fontSize:"xl",color:"gray.500",maxW:"3xl",children:"Our personalized AI approach creates measurable results for professionals across all industries"})]}),u.jsx(A,{size:"lg",colorScheme:"blue",px:8,py:6,fontSize:"lg",fontWeight:"600",borderRadius:"xl",onClick:()=>e?r(!0):s("/login"),_hover:{transform:"translateY(-2px)",boxShadow:"xl"},transition:"all 0.3s",children:e?"Start Your Journey":"Start for FREE"})]})}),u.jsx(f,{maxW:"95%",py:{base:10,md:16},children:u.jsxs(j,{spacing:12,children:[u.jsxs(j,{spacing:4,textAlign:"center",children:[u.jsx(w,{size:"2xl",fontWeight:"black",color:a,children:"Everything You Need to Land Your Dream Job"}),u.jsx(y,{fontSize:"xl",color:"gray.500",maxW:"3xl",children:"Our comprehensive suite of AI-powered tools gives you the competitive edge in today's job market."})]}),u.jsx(W,{templateColumns:{base:"repeat(2, 1fr)",md:"repeat(2, 1fr)",lg:"repeat(3, 1fr)"},gap:{base:4,md:8},children:_.map(((e,r)=>u.jsx(b,{bg:o,borderRadius:{base:"xl",md:"2xl"},p:{base:4,md:8},border:"1px solid",borderColor:g("gray.200","gray.700"),_hover:{transform:"translateY(-4px)",boxShadow:"xl"},transition:"all 0.3s",h:"full",children:u.jsxs(j,{spacing:{base:3,md:4},align:"start",h:"full",children:[u.jsx(S,{as:e.icon,boxSize:{base:8,md:10},color:e.color}),u.jsx(w,{size:{base:"sm",md:"md"},fontWeight:"bold",color:a,children:e.title}),u.jsx(y,{color:"gray.500",lineHeight:"tall",fontSize:{base:"sm",md:"md"},children:e.description})]})},r)))})]})}),u.jsx(f,{maxW:"95%",py:{base:10,md:16},children:u.jsx(j,{spacing:12,children:u.jsxs(j,{spacing:8,textAlign:"center",maxW:"3xl",mx:"auto",children:[u.jsx(w,{size:"2xl",fontWeight:"black",color:a,children:"Ready to Transform Your Career?"}),u.jsx(y,{fontSize:"xl",color:"gray.500",children:"Join thousands of professionals who are already using AI-powered tools to advance their careers"}),u.jsx(A,{size:"lg",colorScheme:"blue",px:12,py:8,fontSize:"xl",fontWeight:"700",borderRadius:"2xl",onClick:()=>e?r(!0):s("/login"),_hover:{transform:"translateY(-3px)",boxShadow:"0 20px 40px rgba(66, 153, 225, 0.4)"},transition:"all 0.3s",children:e?"Start Your Journey":"Begin Your Career Transformation"})]})})}),u.jsx(x,{isOpen:!1,onOpen:()=>{},onClose:()=>{},credits:e?.credits||0}),u.jsx(h,{isOpen:!1,onOpen:()=>{},onClose:()=>{}}),u.jsx(k.Suspense,{fallback:u.jsx(u.Fragment,{}),children:u.jsx(O,{isOpen:!1,onClose:()=>{},userCredits:e?.credits||0,feature:"cover-letter"})})]})};export{Y as default};
