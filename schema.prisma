datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                   Int              @id @default(autoincrement())
  username             String           @unique
  email                String           @unique
  hasPaid              Boolean          @default(false)
  gptModel             String           @default("gpt-4o-mini")
  datePaid             DateTime?
  stripeId             String?
  checkoutSessionId    String?
  subscriptionStatus   String?
  notifyPaymentExpires Boolean          @default(false)
  credits              Int              @default(3)
  yearsOfExperience    Int?             @default(0)

  // Admin role support (temporarily commented for compilation)
  // role                 String           @default("user") // "user", "admin", "super_admin"
  // isAdmin              <PERSON>          @default(false)

  // Profile settings
  profileImageUrl      String?
  bio                  String?

  // User preferences
  darkMode             Boolean          @default(false)
  wordCountDisplay     Boolean          @default(true)
  autoSave             Boolean          @default(true)

  // Notification preferences
  jobReminders         Boolean          @default(true)
  featureUpdates       <PERSON><PERSON><PERSON>          @default(true)
  subscriptionReminders <PERSON>olean         @default(true)
  marketingEmails      Boolean          @default(false)

  // Account settings
  lastLoginAt          DateTime?
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @default(now()) @updatedAt

  letters              CoverLetter[]
  jobs                 Job[]
  resumes              Resume[]
  jobApplications      JobApplication[]
  learningProgress     LearningProgress[]
  userBadges           UserBadge[]
  interviewQuestionSets InterviewQuestionSet[]
  savedInterviewQuestions SavedInterviewQuestion[]
  practiceAnswers      PracticeAnswer[]
  feedback             Feedback[]
}

model CoverLetter {
  id         String    @id @default(uuid())
  title      String
  content    String
  tokenUsage Int
  job        Job       @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId      String
  user       User?     @relation(fields: [userId], references: [id])
  userId     Int?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime? @updatedAt
}

model Job {
  id          String           @id @default(uuid())
  title       String
  company     String
  location    String
  description String
  coverLetter CoverLetter[]
  user        User?            @relation(fields: [userId], references: [id])
  userId      Int?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  isCompleted Boolean          @default(false)
  applications JobApplication[]
}

model JobApplication {
  id            String    @id @default(uuid())
  job           Job       @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId         String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId        Int
  dateApplied   DateTime
  status        String    // "Saved", "Applied", "Interview", "Offer", "Rejected"
  notes         String?
  salary        String?
  contactPerson String?
  contactEmail  String?
  followUpDate  DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Resume {
  id          String   @id @default(uuid())
  title       String
  templateId  String?
  personalInfo Json
  summary     String
  experience  Json
  education   Json
  skills      Json
  certifications Json?
  fileData    Json?
  user        User     @relation(fields: [userId], references: [id])
  userId      Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
}

model LearningProgress {
  id           String   @id @default(uuid())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       Int
  resourceId   String   // ID of the learning resource
  resourceType String   // "video", "article", etc.
  progress     Int      @default(0) // 0-100
  completed    Boolean  @default(false)
  timeSpent    Int      @default(0) // in seconds
  lastAccessed DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([userId, resourceId])
  @@index([userId])
}

model Badge {
  id          String      @id @default(uuid())
  name        String      @unique
  description String
  icon        String      // Icon name or emoji
  category    String      // "learning", "experience", "achievement"
  criteria    Json        // Criteria for earning the badge
  color       String      @default("purple")
  createdAt   DateTime    @default(now())
  userBadges  UserBadge[]
}

model UserBadge {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    Int
  badge     Badge    @relation(fields: [badgeId], references: [id], onDelete: Cascade)
  badgeId   String
  earnedAt  DateTime @default(now())

  @@unique([userId, badgeId])
  @@index([userId])
}

model InterviewQuestionSet {
  id              String   @id @default(uuid())
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId          Int
  jobId           String
  jobTitle        String
  company         String
  jobDescription  String
  questions       Json     // Array of questions with category, question, tips, answer, references
  generatedAt     DateTime @default(now())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
  @@index([userId, jobId])
}

model SavedInterviewQuestion {
  id          String   @id @default(uuid())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  questionType String  // "common" or "generated"
  questionId  String? // For common questions
  questionData Json    // Full question data for generated questions
  notes       String?
  savedAt     DateTime @default(now())

  @@index([userId])
  @@unique([userId, questionType, questionId])
}

model PracticeAnswer {
  id           String   @id @default(uuid())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       Int
  questionType String   // "common" or "generated"
  questionData Json     // Full question data
  userAnswer   String
  notes        String?
  savedAt      DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([userId])
}

// Admin Analytics Models
model SystemMetrics {
  id              String   @id @default(uuid())
  metricType      String   // "performance", "usage", "error", "financial"
  metricName      String   // "response_time", "active_users", "error_rate", etc.
  value           Float
  unit            String?  // "ms", "count", "percentage", "currency"
  timestamp       DateTime @default(now())
  metadata        Json?    // Additional context data
  
  @@index([metricType, metricName, timestamp])
  @@index([timestamp])
}

model UserActivity {
  id              String   @id @default(uuid())
  userId          Int?
  sessionId       String?
  action          String   // "login", "create_letter", "generate_resume", etc.
  resource        String?  // The resource affected (letter_id, job_id, etc.)
  details         Json?    // Additional activity details
  ipAddress       String?
  userAgent       String?
  duration        Int?     // Action duration in milliseconds
  timestamp       DateTime @default(now())
  
  @@index([userId, timestamp])
  @@index([action, timestamp])
  @@index([timestamp])
}

model ErrorLog {
  id              String   @id @default(uuid())
  errorType       String   // "client", "server", "database", "external_api"
  severity        String   // "low", "medium", "high", "critical"
  message         String
  stackTrace      String?
  userId          Int?
  sessionId       String?
  endpoint        String?
  method          String?
  statusCode      Int?
  userAgent       String?
  ipAddress       String?
  additionalData  Json?
  resolved        Boolean  @default(false)
  resolvedAt      DateTime?
  resolvedBy      String?
  timestamp       DateTime @default(now())
  
  @@index([errorType, severity, timestamp])
  @@index([userId, timestamp])
  @@index([resolved, timestamp])
}

model FeatureUsage {
  id              String   @id @default(uuid())
  featureName     String   // "cover_letter_generator", "resume_builder", etc.
  userId          Int?
  usageCount      Int      @default(1)
  successRate     Float?   // Percentage of successful uses
  avgDuration     Float?   // Average time spent using the feature
  lastUsed        DateTime @default(now())
  date            DateTime @default(now()) // For daily aggregation
  
  @@unique([featureName, userId, date])
  @@index([featureName, date])
  @@index([userId, date])
}

model AdminAuditLog {
  id              String   @id @default(uuid())
  adminUserId     Int
  action          String   // "user_update", "system_config", "data_export", etc.
  targetResource  String?  // What was affected
  targetId        String?  // ID of affected resource
  changes         Json?    // What changes were made
  ipAddress       String?
  userAgent       String?
  timestamp       DateTime @default(now())

  @@index([adminUserId, timestamp])
  @@index([action, timestamp])
  @@index([timestamp])
}

model Feedback {
  id              String   @id @default(uuid())
  user            User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId          Int?
  guestEmail      String?  // For non-authenticated users
  type            String   // "bug", "feature", "improvement", "general", "other"
  description     String
  screenshot      String?  // Base64 encoded screenshot
  url             String?  // Page where feedback was submitted
  userAgent       String?  // Browser/device information
  status          String   @default("new") // "new", "in_progress", "resolved", "closed"
  adminNotes      String?  // Admin can add notes
  priority        String   @default("medium") // "low", "medium", "high", "urgent"
  assignedTo      String?  // Admin user who is handling this
  resolvedAt      DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([status, createdAt])
  @@index([type, createdAt])
  @@index([userId, createdAt])
  @@index([createdAt])
}
