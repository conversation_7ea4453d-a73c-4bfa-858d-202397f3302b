{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "type": "module", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"build": "wasp build", "build:analyze": "wasp build --analyze", "build:prod": "NODE_ENV=production wasp build", "dev": "wasp start", "test": "wasp test", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "lighthouse": "lighthouse https://careerdart.com --output json --output-path ./lighthouse-report.json --chrome-flags=\"--headless\"", "lighthouse:local": "lighthouse http://localhost:3000 --output json --output-path ./lighthouse-local.json --chrome-flags=\"--headless\"", "performance:test": "npm run lighthouse && npm run lighthouse:local", "optimize": "npm run build:prod && npm run lighthouse", "build:optimized": "bash scripts/build-optimized.sh", "build:netlify": "bash scripts/build-netlify-ultimate.sh", "build:netlify:final": "bash scripts/build-netlify-final.sh", "build:netlify:robust": "bash scripts/build-netlify-robust.sh", "build:netlify:simple": "bash scripts/build-netlify-simple.sh", "build:netlify:original": "bash scripts/build-netlify.sh", "build:fly": "wasp build", "start": "wasp start", "test:e2e": "echo 'E2E tests will be available after wasp start'", "analyze": "cd .wasp/build/web-app && npm run build -- --analyze", "analyze:webpack": "npm run analyze -- --webpack", "vercel-build": "bash scripts/build-for-vercel.sh", "build:production": "NODE_ENV=production npm run vercel-build", "build:local": "bash scripts/build-local-for-vercel.sh", "deploy": "wasp deploy", "deploy:netlify": "npm run build:netlify && echo 'Deploy the dist/ directory to Netlify'", "deploy:fly": "bash scripts/deploy-fly.sh", "deploy:preview": "vercel", "deploy:local": "bash scripts/deploy-to-vercel.sh", "railway:deploy": "railway up", "railway:build": "bash scripts/simple-railway-build.sh", "db:generate": "wasp db generate", "db:migrate": "wasp db migrate-deploy", "db:migrate-dev": "wasp db migrate-dev", "db:studio": "wasp db studio", "studio": "wasp db studio", "performance:audit": "lighthouse https://careerdart.netlify.app --output=html --output-path=./performance-audit.html --chrome-flags=\"--headless --no-sandbox\"", "performance:ci": "lhci autorun", "bundle:analyze": "cd .wasp/build/web-app && npx vite-bundle-analyzer build", "test:performance": "lighthouse https://careerdart.netlify.app --output=json --output-path=./lighthouse-report.json --chrome-flags=\"--headless --no-sandbox\" --quiet", "precommit": "npm run build:optimized && npm run test:performance", "health:check": "node scripts/health-check.js"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "2.8.0", "@emotion/react": "11.10.6", "@emotion/styled": "11.10.6", "@lucia-auth/adapter-prisma": "^4.0.1", "@node-rs/argon2": "^1.8.3", "@prisma/engines": "5.19.1", "@types/cors": "^2.8.5", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.2.1", "bolt11": "1.4.1", "chart.js": "^4.4.9", "cors": "^2.8.5", "deepmerge": "^4.3.1", "express": "~4.21.0", "express-rate-limit": "^7.1.5", "framer-motion": "^10.12.16", "helmet": "^6.0.0", "jsonwebtoken": "^8.5.1", "lnurl": "0.24.2", "node-fetch": "3.3.0", "openai": "^4.103.0", "pdfjs-dist": "3.3.122", "pg": "^8.16.0", "pg-types": "^4.0.2", "qrcode.react": "3.1.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-icons": "4.8.0", "react-router-dom": "^6.26.2", "resend": "^4.5.1", "std-env": "^3.9.0", "stripe": "13.3.0", "uuid": "^11.1.0", "wasp": "file:.wasp/out/sdk/wasp", "web-vitals": "^5.0.2", "zod": "^3.22.4"}, "devDependencies": {"@flydotio/dockerfile": "^0.7.10", "@prisma/client": "5.19.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@tsconfig/node18": "latest", "@types/body-parser": "^1.19.5", "@types/express": "^4.17.13", "@types/express-serve-static-core": "^4.17.13", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^8.5.9", "@types/node": "^18.0.0", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vercel/node": "^3.0.0", "@vercel/static-build": "^2.0.0", "cssnano": "^7.0.7", "jest": "^29.7.0", "netlify-plugin-cache": "^1.0.3", "prisma": "5.19.1", "rollup": "^4.9.6", "terser": "^5.36.0", "typescript": "^5.1.0", "vercel": "^42.1.1", "vite": "^4.3.9"}, "overrides": {"@prisma/client": "5.19.1", "typescript": "^5.1.0"}}