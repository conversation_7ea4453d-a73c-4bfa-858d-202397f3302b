import { HttpError } from 'wasp/server';
import { emailSender } from 'wasp/server/email';

export const submitFeedback = async (args, context) => {
  // Allow both authenticated and non-authenticated users to submit feedback
  const { type, description, screenshot, url, userAgent, guestEmail } = args;

  // Validate required fields
  if (!type || !description) {
    throw new HttpError(400, 'Feedback type and description are required');
  }

  // Validate feedback type
  const validTypes = ['bug', 'feature', 'improvement', 'general', 'other'];
  if (!validTypes.includes(type)) {
    throw new HttpError(400, 'Invalid feedback type');
  }

  // Validate description length
  if (description.length < 10) {
    throw new HttpError(400, 'Description must be at least 10 characters long');
  }

  if (description.length > 5000) {
    throw new HttpError(400, 'Description must be less than 5000 characters');
  }

  try {
    // Create feedback object
    const feedbackData = {
      userId: context.user?.id || 'guest',
      userEmail: context.user?.email || guestEmail || 'Anonymous user',
      type,
      description,
      screenshot: screenshot || null,
      url: url || 'Unknown',
      userAgent: userAgent || 'Unknown',
      timestamp: new Date().toISOString(),
      status: 'new',
      isGuest: !context.user
    };

    // In a real application, you would save this to a database
    // For now, we'll log it and potentially send it via email
    console.log('📝 New Feedback Received:', {
      id: `feedback_${Date.now()}`,
      user: context.user.email || context.user.id,
      type: feedbackData.type,
      description: feedbackData.description.substring(0, 100) + '...',
      hasScreenshot: !!feedbackData.screenshot,
      url: feedbackData.url,
      timestamp: feedbackData.timestamp
    });

    // TODO: In production, you might want to:
    // 1. Save to a Feedback table in the database
    // 2. Send email notification to admin
    // 3. Create a ticket in your issue tracking system
    // 4. Send to analytics/monitoring service

    // For now, we'll simulate saving to database
    // You could add a Feedback entity to your schema.prisma and save it here
    
    // Example of what you might do:
    // const feedback = await context.entities.Feedback.create({
    //   data: {
    //     userId: context.user.id,
    //     type: feedbackData.type,
    //     description: feedbackData.description,
    //     screenshot: feedbackData.screenshot,
    //     url: feedbackData.url,
    //     userAgent: feedbackData.userAgent,
    //     status: 'new',
    //     createdAt: new Date()
    //   }
    // });

    // Send email notification to admin
    try {
      await sendAdminNotification(feedbackData);
    } catch (emailError) {
      console.warn('Failed to send admin notification:', emailError);
      // Don't fail the entire request if email fails
    }

    return {
      success: true,
      message: 'Feedback submitted successfully',
      feedbackId: `feedback_${Date.now()}`,
      timestamp: feedbackData.timestamp
    };

  } catch (error) {
    console.error('Error submitting feedback:', error);
    throw new HttpError(500, 'Failed to submit feedback. Please try again.');
  }
};

// Helper function to send admin notification
const sendAdminNotification = async (feedbackData) => {
  const userType = feedbackData.isGuest ? 'Guest User' : 'Registered User';
  const userInfo = feedbackData.isGuest ?
    `Guest Email: ${feedbackData.userEmail}` :
    `User ID: ${feedbackData.userId}\nEmail: ${feedbackData.userEmail}`;

  const emailContent = `
    🔔 New ${feedbackData.type.toUpperCase()} Feedback Received

    👤 User Information:
    ${userInfo}
    Type: ${userType}

    📝 Feedback Details:
    Type: ${feedbackData.type}
    Description: ${feedbackData.description}

    🌐 Technical Information:
    URL: ${feedbackData.url}
    User Agent: ${feedbackData.userAgent}
    Timestamp: ${feedbackData.timestamp}

    📸 Screenshot: ${feedbackData.screenshot ? 'Attached' : 'No screenshot provided'}

    ---
    This feedback was submitted through the CareerDart feedback widget.
  `;

  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Feedback - CareerDart</title>
      </head>
      <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #0080FF; margin: 0; font-size: 24px;">Career<span style="color: #5CAEFF;">Dart</span></h1>
          <p style="color: #666; margin: 5px 0 0 0;">Admin Notification</p>
        </div>

        <div style="background: #f8fafc; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h2 style="color: #1e293b; margin-top: 0;">🔔 New ${feedbackData.type.charAt(0).toUpperCase() + feedbackData.type.slice(1)} Feedback</h2>

          <div style="background: white; border-radius: 6px; padding: 15px; margin: 15px 0;">
            <h3 style="color: #475569; margin-top: 0;">👤 User Information</h3>
            <p style="margin: 5px 0;"><strong>Type:</strong> ${userType}</p>
            <p style="margin: 5px 0;"><strong>Email:</strong> ${feedbackData.userEmail}</p>
            ${!feedbackData.isGuest ? `<p style="margin: 5px 0;"><strong>User ID:</strong> ${feedbackData.userId}</p>` : ''}
          </div>

          <div style="background: white; border-radius: 6px; padding: 15px; margin: 15px 0;">
            <h3 style="color: #475569; margin-top: 0;">📝 Feedback Details</h3>
            <p style="margin: 5px 0;"><strong>Type:</strong> <span style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">${feedbackData.type}</span></p>
            <p style="margin: 10px 0 5px 0;"><strong>Description:</strong></p>
            <div style="background: #f1f5f9; padding: 10px; border-radius: 4px; border-left: 3px solid #0080FF;">
              ${feedbackData.description.replace(/\n/g, '<br>')}
            </div>
          </div>

          <div style="background: white; border-radius: 6px; padding: 15px; margin: 15px 0;">
            <h3 style="color: #475569; margin-top: 0;">🌐 Technical Information</h3>
            <p style="margin: 5px 0;"><strong>URL:</strong> <a href="${feedbackData.url}" style="color: #0080FF;">${feedbackData.url}</a></p>
            <p style="margin: 5px 0;"><strong>User Agent:</strong> <code style="background: #f1f5f9; padding: 2px 4px; border-radius: 3px; font-size: 11px;">${feedbackData.userAgent}</code></p>
            <p style="margin: 5px 0;"><strong>Timestamp:</strong> ${new Date(feedbackData.timestamp).toLocaleString()}</p>
            <p style="margin: 5px 0;"><strong>Screenshot:</strong> ${feedbackData.screenshot ? '✅ Attached' : '❌ No screenshot provided'}</p>
          </div>
        </div>

        <div style="text-align: center; font-size: 12px; color: #94a3b8; border-top: 1px solid #e2e8f0; padding-top: 15px;">
          <p>This feedback was submitted through the CareerDart feedback widget.</p>
          <p>© 2024 CareerDart. All rights reserved.</p>
        </div>
      </body>
    </html>
  `;

  await emailSender.send({
    to: '<EMAIL>', // Admin email - you can change this
    subject: `🔔 New ${feedbackData.type} feedback from ${feedbackData.userEmail}`,
    text: emailContent,
    html: htmlContent
  });

  console.log('✅ Admin notification email sent successfully');
};
