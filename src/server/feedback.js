import { HttpError } from 'wasp/server';

export const submitFeedback = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'You must be logged in to submit feedback');
  }

  const { type, description, screenshot, url, userAgent } = args;

  // Validate required fields
  if (!type || !description) {
    throw new HttpError(400, 'Feedback type and description are required');
  }

  // Validate feedback type
  const validTypes = ['bug', 'feature', 'improvement', 'general', 'other'];
  if (!validTypes.includes(type)) {
    throw new HttpError(400, 'Invalid feedback type');
  }

  // Validate description length
  if (description.length < 10) {
    throw new HttpError(400, 'Description must be at least 10 characters long');
  }

  if (description.length > 5000) {
    throw new HttpError(400, 'Description must be less than 5000 characters');
  }

  try {
    // Create feedback object
    const feedbackData = {
      userId: context.user.id,
      userEmail: context.user.email || 'No email provided',
      type,
      description,
      screenshot: screenshot || null,
      url: url || 'Unknown',
      userAgent: userAgent || 'Unknown',
      timestamp: new Date().toISOString(),
      status: 'new'
    };

    // In a real application, you would save this to a database
    // For now, we'll log it and potentially send it via email
    console.log('📝 New Feedback Received:', {
      id: `feedback_${Date.now()}`,
      user: context.user.email || context.user.id,
      type: feedbackData.type,
      description: feedbackData.description.substring(0, 100) + '...',
      hasScreenshot: !!feedbackData.screenshot,
      url: feedbackData.url,
      timestamp: feedbackData.timestamp
    });

    // TODO: In production, you might want to:
    // 1. Save to a Feedback table in the database
    // 2. Send email notification to admin
    // 3. Create a ticket in your issue tracking system
    // 4. Send to analytics/monitoring service

    // For now, we'll simulate saving to database
    // You could add a Feedback entity to your schema.prisma and save it here
    
    // Example of what you might do:
    // const feedback = await context.entities.Feedback.create({
    //   data: {
    //     userId: context.user.id,
    //     type: feedbackData.type,
    //     description: feedbackData.description,
    //     screenshot: feedbackData.screenshot,
    //     url: feedbackData.url,
    //     userAgent: feedbackData.userAgent,
    //     status: 'new',
    //     createdAt: new Date()
    //   }
    // });

    // Send email notification to admin (optional)
    try {
      // You could integrate with your email service here
      // await sendAdminNotification(feedbackData);
    } catch (emailError) {
      console.warn('Failed to send admin notification:', emailError);
      // Don't fail the entire request if email fails
    }

    return {
      success: true,
      message: 'Feedback submitted successfully',
      feedbackId: `feedback_${Date.now()}`,
      timestamp: feedbackData.timestamp
    };

  } catch (error) {
    console.error('Error submitting feedback:', error);
    throw new HttpError(500, 'Failed to submit feedback. Please try again.');
  }
};

// Helper function to send admin notification (you can implement this)
const sendAdminNotification = async (feedbackData) => {
  // This is where you would integrate with your email service
  // Example with Resend (if you're using it):
  /*
  const emailContent = `
    New feedback received from ${feedbackData.userEmail}
    
    Type: ${feedbackData.type}
    Description: ${feedbackData.description}
    URL: ${feedbackData.url}
    User Agent: ${feedbackData.userAgent}
    Timestamp: ${feedbackData.timestamp}
    
    ${feedbackData.screenshot ? 'Screenshot attached' : 'No screenshot'}
  `;
  
  await resend.emails.send({
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: `New ${feedbackData.type} feedback from ${feedbackData.userEmail}`,
    text: emailContent,
    attachments: feedbackData.screenshot ? [{
      filename: 'screenshot.png',
      content: feedbackData.screenshot.split(',')[1], // Remove data:image/png;base64, prefix
    }] : []
  });
  */
};
