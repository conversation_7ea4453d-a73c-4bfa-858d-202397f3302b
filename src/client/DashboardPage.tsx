import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Grid,
  GridItem,
  Heading,
  Text,
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Badge,
  Button,
  SimpleGrid,
  Progress,
  Avatar,
  useColorModeValue,
  Container,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Divider,
  Link,
  useToast,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  Icon,
  Circle,
  useBreakpointValue,
} from '@chakra-ui/react';
import {
  FiFileText,
  FiBriefcase,
  FiUser,
  FiTrendingUp,
  FiCalendar,
  FiPlus,
  FiEdit,
  FiEye,
  FiArrowRight,
  FiAward,
  FiZap,
  FiTarget,
  FiChevronLeft,
  FiChevronRight
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import { useAuth } from 'wasp/client/auth';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useQuery, getCoverLetters, getJobs } from 'wasp/client/operations';

// Motion components for animations
const MotionBox = motion(Box);
const MotionCard = motion(Card);

const Dashboard: React.FC = () => {
  const { data: user } = useAuth();
  const navigate = useNavigate();
  const toast = useToast();

  // Pagination state
  const [activityPage, setActivityPage] = useState(1);
  const [jobsPage, setJobsPage] = useState(1);
  const itemsPerPage = 2; // Changed from 5 to 2 to show max 2 items

  // Enhanced color scheme with responsive considerations
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('brand.500', 'brand.400');

  // Responsive values for layout
  const isMobile = useBreakpointValue({ base: true, md: false });
  const containerPadding = useBreakpointValue({ base: 4, md: 8 });
  const cardSpacing = useBreakpointValue({ base: 4, md: 8 });
  const headerSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const avatarSize = useBreakpointValue({ base: 'lg', md: 'xl' });

  // Fetch user's data
  const { data: coverLetters, isLoading: coverLettersLoading } = useQuery(getCoverLetters);
  const { data: jobs, isLoading: jobsLoading } = useQuery(getJobs);

  const isLoading = coverLettersLoading || jobsLoading;

  // Calculate enhanced stats
  const totalCoverLetters = coverLetters?.length || 0;
  const totalJobs = jobs?.length || 0;
  const recentCoverLetters = coverLetters?.slice(0, 3) || [];
  const recentJobs = jobs?.slice(0, 3) || [];

  // Calculate success metrics
  const thisMonth = new Date();
  thisMonth.setDate(1);
  const thisMonthCoverLetters = coverLetters?.filter(cl => new Date(cl.createdAt) >= thisMonth).length || 0;
  const thisMonthJobs = jobs?.filter(job => new Date(job.createdAt) >= thisMonth).length || 0;

  // Get recent activity (last 7 days)
  const lastWeek = new Date();
  lastWeek.setDate(lastWeek.getDate() - 7);
  
  const allRecentActivity = [
    ...(coverLetters?.filter(cl => new Date(cl.createdAt) > lastWeek).map(cl => ({
      type: 'cover_letter',
      title: `Cover letter - ${cl.title || 'Untitled'}`,
      date: cl.createdAt,
      action: 'created'
    })) || []),
    ...(jobs?.filter(job => new Date(job.createdAt) > lastWeek).map(job => ({
      type: 'job',
      title: job.title,
      date: job.createdAt,
      action: 'added'
    })) || [])
  ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // Paginated recent activity
  const totalActivityPages = Math.ceil(allRecentActivity.length / itemsPerPage);
  const paginatedActivity = allRecentActivity.slice(
    (activityPage - 1) * itemsPerPage,
    activityPage * itemsPerPage
  );

  // Paginated recent jobs
  const totalJobsPages = Math.ceil((recentJobs?.length || 0) / itemsPerPage);
  const paginatedJobs = recentJobs.slice(
    (jobsPage - 1) * itemsPerPage,
    jobsPage * itemsPerPage
  );

  if (isLoading) {
    return (
      <Box bg={bgGradient} minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <Center>
          <VStack spacing={6}>
            <Spinner size="xl" color={accentColor} thickness="4px" />
            <VStack spacing={2}>
              <Text fontSize="lg" fontWeight="600" color={textColor}>Loading your dashboard...</Text>
              <Text fontSize="sm" color="gray.500">Preparing your career insights</Text>
            </VStack>
          </VStack>
        </Center>
      </Box>
    );
  }

  return (
    <Box bg={bgGradient} minH="100vh" py={{ base: 2, md: 6 }}>
      <Container maxW={{ base: "100%", md: "95%" }} px={{ base: 2, md: 4, lg: 6 }}>
        <VStack spacing={{ base: 4, md: 6 }} align="stretch">
          {/* Welcome Header Content - Responsive */}
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Flex
              direction={{ base: 'column', sm: 'row' }}
              gap={{ base: 2, sm: 4 }}
              align={{ base: 'center', sm: 'flex-start' }}
              p={{ base: 3, md: 6 }}
              bg={cardBg}
              borderRadius={{ base: "lg", md: "xl" }}
              borderWidth="1px"
              borderColor={borderColor}
              shadow="sm"
              textAlign={{ base: 'center', sm: 'left' }}
              w="full"
            >
              <Avatar
                size={{ base: "lg", md: "xl" }}
                name={user?.username || 'User'}
                bg={accentColor}
                color="white"
                shadow="md"
                flexShrink={0}
              />
              <VStack spacing={{ base: 2, md: 3 }} align={{ base: 'center', sm: 'start' }} flex={1} w="full">
                <Heading
                  size={{ base: "md", md: "lg" }}
                  color={textColor}
                  fontWeight="700"
                  textAlign={{ base: 'center', sm: 'left' }}
                  w="full"
                >
                  Welcome back, {user?.username || 'User'}!
                </Heading>
                <Text
                  color="gray.500"
                  fontSize={{ base: 'sm', md: 'md' }}
                  fontWeight="500"
                  textAlign={{ base: 'center', sm: 'left' }}
                >
                  Your career progress overview
                </Text>
                <Flex
                  direction={{ base: 'column', sm: 'row' }}
                  mt={{ base: 2, md: 1 }}
                  gap={{ base: 1, sm: 3 }}
                  w="full"
                  justify={{ base: 'center', sm: 'flex-start' }}
                  align="center"
                  wrap="wrap"
                >
                  <Badge
                    colorScheme="green"
                    px={{ base: 1, md: 2 }}
                    py={1}
                    borderRadius="md"
                    fontSize={{ base: "2xs", md: "xs" }}
                    textAlign="center"
                    maxW={{ base: "140px", sm: "none" }}
                    whiteSpace="nowrap"
                    overflow="hidden"
                    textOverflow="ellipsis"
                  >
                    {thisMonthCoverLetters} letters
                  </Badge>
                  <Badge
                    colorScheme="blue"
                    px={{ base: 1, md: 2 }}
                    py={1}
                    borderRadius="md"
                    fontSize={{ base: "2xs", md: "xs" }}
                    textAlign="center"
                    maxW={{ base: "140px", sm: "none" }}
                    whiteSpace="nowrap"
                    overflow="hidden"
                    textOverflow="ellipsis"
                  >
                    {thisMonthJobs} apps tracked
                  </Badge>
                </Flex>
              </VStack>
            </Flex>
          </MotionBox>

          {/* Enhanced Stats Grid - Responsive columns */}
          <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={{ base: 4, md: 6 }}>
            <MotionCard
              bg={cardBg}
              border="1px"
              borderColor={borderColor}
              borderRadius="2xl"
              overflow="hidden"
              _hover={{ transform: 'translateY(-4px)', boxShadow: 'xl' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -4 }}
              transition={{
                layout: { duration: 0.6, delay: 0.1 },
                hover: { duration: 0.2 }
              }}
            >
              <Box bgGradient="linear(to-r, blue.400, blue.500)" h="4px" />
              <CardBody p={{ base: 4, md: 6 }}>
                <HStack spacing={4}>
                  <Circle size={{ base: "50px", md: "60px" }} bg="blue.50" color="blue.500">
                    <Icon as={FiFileText} boxSize={{ base: 5, md: 6 }} />
                  </Circle>
                  <Stat>
                    <StatLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="600" color="gray.500">Cover Letters</StatLabel>
                    <StatNumber fontSize={{ base: "2xl", md: "3xl" }} fontWeight="700" color="blue.500">{totalCoverLetters}</StatNumber>
                    <StatHelpText fontSize="xs">
                      <StatArrow type="increase" />
                      Total created
                    </StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </MotionCard>

            <MotionCard
              bg={cardBg}
              border="1px"
              borderColor={borderColor}
              borderRadius="2xl"
              overflow="hidden"
              _hover={{ transform: 'translateY(-4px)', boxShadow: 'xl' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -4 }}
              transition={{
                layout: { duration: 0.6, delay: 0.2 },
                hover: { duration: 0.2 }
              }}
            >
              <Box bgGradient="linear(to-r, green.400, green.500)" h="4px" />
              <CardBody p={{ base: 4, md: 6 }}>
                <HStack spacing={4}>
                  <Circle size={{ base: "50px", md: "60px" }} bg="green.50" color="green.500">
                    <Icon as={FiBriefcase} boxSize={{ base: 5, md: 6 }} />
                  </Circle>
                  <Stat>
                    <StatLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="600" color="gray.500">Job Applications</StatLabel>
                    <StatNumber fontSize={{ base: "2xl", md: "3xl" }} fontWeight="700" color="green.500">{totalJobs}</StatNumber>
                    <StatHelpText fontSize="xs">
                      <StatArrow type="increase" />
                      Total tracked
                    </StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </MotionCard>

            <MotionCard
              bg={cardBg}
              border="1px"
              borderColor={borderColor}
              borderRadius="2xl"
              overflow="hidden"
              _hover={{ transform: 'translateY(-4px)', boxShadow: 'xl' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -4 }}
              transition={{
                layout: { duration: 0.6, delay: 0.3 },
                hover: { duration: 0.2 }
              }}
            >
              <Box bgGradient="linear(to-r, purple.400, purple.500)" h="4px" />
              <CardBody p={{ base: 4, md: 6 }}>
                <HStack spacing={4}>
                  <Circle size={{ base: "50px", md: "60px" }} bg="purple.50" color="purple.500">
                    <Icon as={FiZap} boxSize={{ base: 5, md: 6 }} />
                  </Circle>
                  <Stat>
                    <StatLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="600" color="gray.500">Credits</StatLabel>
                    <StatNumber fontSize={{ base: "2xl", md: "3xl" }} fontWeight="700" color="purple.500">{user?.credits || 0}</StatNumber>
                    <StatHelpText fontSize="xs">Available credits</StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </MotionCard>

            <MotionCard
              bg={cardBg}
              border="1px"
              borderColor={borderColor}
              borderRadius="2xl"
              overflow="hidden"
              _hover={{ transform: 'translateY(-4px)', boxShadow: 'xl' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -4 }}
              transition={{
                layout: { duration: 0.6, delay: 0.4 },
                hover: { duration: 0.2 }
              }}
            >
              <Box bgGradient="linear(to-r, orange.400, orange.500)" h="4px" />
              <CardBody p={{ base: 4, md: 6 }}>
                <HStack spacing={4}>
                  <Circle size={{ base: "50px", md: "60px" }} bg="orange.50" color="orange.500">
                    <Icon as={FiAward} boxSize={{ base: 5, md: 6 }} />
                  </Circle>
                  <Stat>
                    <StatLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="600" color="gray.500">Account Status</StatLabel>
                    <StatNumber>
                      <Badge 
                        colorScheme={user?.hasPaid ? 'green' : 'gray'} 
                        fontSize={{ base: "md", md: "lg" }}
                        px={3}
                        py={1}
                        borderRadius="full"
                        fontWeight="600"
                      >
                        {user?.hasPaid ? '✨ Premium' : 'Free'}
                      </Badge>
                    </StatNumber>
                    <StatHelpText fontSize="xs">Current plan</StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </MotionCard>
          </SimpleGrid>

          {/* Enhanced Quick Actions - Responsive grid */}
          <MotionCard
            bg={cardBg}
            border="1px"
            borderColor={borderColor}
            borderRadius="2xl"
            overflow="hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <CardHeader>
              <HStack justify="space-between" align="center">
                <VStack spacing={1} align="start">
                  <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="700">Quick Actions</Heading>
                  <Text color="gray.500" fontSize="sm">Build your career</Text>
                </VStack>
                <Circle size="40px" bg="brand.50" color={accentColor}>
                  <Icon as={FiTarget} boxSize={5} />
                </Circle>
              </HStack>
            </CardHeader>
            <CardBody>
              <SimpleGrid columns={{ base: 2, md: 2, lg: 4 }} spacing={{ base: 2, md: 4 }}>
                <Button
                  as={RouterLink}
                  to="/cover-letters"
                  leftIcon={<FiFileText />}
                  colorScheme="blue"
                  variant="outline"
                  size={{ base: "sm", md: "lg" }}
                  h={{ base: "60px", md: "70px" }}
                  minH={{ base: "60px", md: "70px" }}
                  borderRadius={{ base: "lg", md: "xl" }}
                  _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                  transition="all 0.2s"
                  fontWeight="600"
                  p={{ base: 2, md: 4 }}
                  flexDirection="column"
                  textAlign="center"
                >
                  <VStack spacing={{ base: 0, md: 1 }}>
                    <Text fontSize={{ base: "xs", md: "md" }} lineHeight="short">Cover Letters</Text>
                    <Text fontSize={{ base: "xs", md: "xs" }} opacity={0.8} display={{ base: "none", md: "block" }}>
                      Create and manage
                    </Text>
                  </VStack>
                </Button>
                <Button
                  as={RouterLink}
                  to="/jobs"
                  leftIcon={<FiBriefcase />}
                  colorScheme="green"
                  variant="outline"
                  size={{ base: "sm", md: "lg" }}
                  h={{ base: "60px", md: "70px" }}
                  minH={{ base: "60px", md: "70px" }}
                  borderRadius={{ base: "lg", md: "xl" }}
                  _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                  transition="all 0.2s"
                  fontWeight="600"
                  p={{ base: 2, md: 4 }}
                  flexDirection="column"
                  textAlign="center"
                >
                  <VStack spacing={{ base: 0, md: 1 }}>
                    <Text fontSize={{ base: "xs", md: "md" }} lineHeight="short">Job Applications</Text>
                    <Text fontSize={{ base: "xs", md: "xs" }} opacity={0.8} display={{ base: "none", md: "block" }}>
                      Track your progress
                    </Text>
                  </VStack>
                </Button>
                <Button
                  as={RouterLink}
                  to="/resume"
                  leftIcon={<FiFileText />}
                  colorScheme="purple"
                  variant="outline"
                  size={{ base: "sm", md: "lg" }}
                  h={{ base: "60px", md: "70px" }}
                  minH={{ base: "60px", md: "70px" }}
                  borderRadius={{ base: "lg", md: "xl" }}
                  _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                  transition="all 0.2s"
                  fontWeight="600"
                  p={{ base: 2, md: 4 }}
                  flexDirection="column"
                  textAlign="center"
                >
                  <VStack spacing={{ base: 0, md: 1 }}>
                    <Text fontSize={{ base: "xs", md: "md" }} lineHeight="short">Build Resume</Text>
                    <Text fontSize={{ base: "xs", md: "xs" }} opacity={0.8} display={{ base: "none", md: "block" }}>
                      Customize your resume
                    </Text>
                  </VStack>
                </Button>
                <Button
                  as={RouterLink}
                  to="/profile"
                  leftIcon={<FiUser />}
                  colorScheme="orange"
                  variant="outline"
                  size={{ base: "sm", md: "lg" }}
                  h={{ base: "60px", md: "70px" }}
                  minH={{ base: "60px", md: "70px" }}
                  borderRadius={{ base: "lg", md: "xl" }}
                  _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                  transition="all 0.2s"
                  fontWeight="600"
                  p={{ base: 2, md: 4 }}
                  flexDirection="column"
                  textAlign="center"
                >
                  <VStack spacing={{ base: 0, md: 1 }}>
                    <Text fontSize={{ base: "xs", md: "md" }} lineHeight="short">Update Profile</Text>
                    <Text fontSize={{ base: "xs", md: "xs" }} opacity={0.8} display={{ base: "none", md: "block" }}>
                      Keep info up-to-date
                    </Text>
                  </VStack>
                </Button>
              </SimpleGrid>
            </CardBody>
          </MotionCard>

          {/* Main Content Grid - Responsive layout */}
          <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
            {/* Left Side - Recent Activity and Job Applications */}
            <GridItem>
              <VStack spacing={6} align="stretch">
                {/* Recent Activity */}
                <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm" h="fit-content">
                  <CardHeader>
                    <Flex justify="space-between" align="center">
                      <Heading size="md">Recent Activity</Heading>
                      <Link as={RouterLink} to="/cover-letters" color="blue.500" fontSize="sm" fontWeight="medium">
                        View all <FiArrowRight style={{ display: 'inline', marginLeft: '4px' }} />
                      </Link>
                    </Flex>
                  </CardHeader>
                  <CardBody>
                    {paginatedActivity.length > 0 ? (
                      <VStack spacing={4} align="stretch">
                        {paginatedActivity.map((activity, index) => (
                          <Box 
                            key={index} 
                            p={4} 
                            bg={useColorModeValue("gray.50", "gray.700")} 
                            borderRadius="md"
                            borderWidth="1px"
                            borderColor={borderColor}
                            transition="all 0.2s"
                            _hover={{ transform: 'translateY(-2px)', boxShadow: 'md' }}
                          >
                            <HStack spacing={3} align="center">
                              <Box
                                p={2}
                                bg={activity.type === 'cover_letter' 
                                  ? useColorModeValue("blue.50", "blue.900") 
                                  : useColorModeValue("green.50", "green.900")}
                                color={activity.type === 'cover_letter' 
                                  ? useColorModeValue("blue.500", "blue.200") 
                                  : useColorModeValue("green.500", "green.200")}
                                borderRadius="md"
                              >
                                {activity.type === 'cover_letter' ? (
                                  <FiFileText />
                                ) : (
                                  <FiBriefcase />
                                )}
                              </Box>
                              <VStack spacing={1} align="start" flex={1}>
                                <Text fontSize="sm" fontWeight="medium">
                                  {activity.title}
                                </Text>
                                <Text fontSize="xs" color="gray.600">
                                  {activity.action} • {new Date(activity.date).toLocaleDateString()}
                                </Text>
                              </VStack>
                            </HStack>
                          </Box>
                        ))}
                        {/* Activity Pagination */}
                        {totalActivityPages > 1 && (
                          <Flex justify="center" mt={4}>
                            <HStack spacing={2}>
                              <Button
                                size="sm"
                                onClick={() => setActivityPage(prev => Math.max(prev - 1, 1))}
                                isDisabled={activityPage === 1}
                                leftIcon={<FiChevronLeft />}
                                variant="outline"
                              >
                                Prev
                              </Button>
                              <Text fontSize="sm" color="gray.500">
                                {activityPage} of {totalActivityPages}
                              </Text>
                              <Button
                                size="sm"
                                onClick={() => setActivityPage(prev => Math.min(prev + 1, totalActivityPages))}
                                isDisabled={activityPage === totalActivityPages}
                                rightIcon={<FiChevronRight />}
                                variant="outline"
                              >
                                Next
                              </Button>
                            </HStack>
                          </Flex>
                        )}
                      </VStack>
                    ) : (
                      <Box textAlign="center" py={6}>
                        <Text color="gray.500">No recent activity</Text>
                        <Text fontSize="sm" color="gray.400" mt={2}>
                          Start by creating your first cover letter or adding a job application
                        </Text>
                        <HStack spacing={4} justify="center" mt={4}>
                          <Button
                            as={RouterLink}
                            to="/cover-letters/new"
                            size="sm"
                            colorScheme="blue"
                            variant="outline"
                            leftIcon={<FiFileText />}
                          >
                            Create Cover Letter
                          </Button>
                          <Button
                            as={RouterLink}
                            to="/jobs/new"
                            size="sm"
                            colorScheme="green"
                            variant="outline"
                            leftIcon={<FiBriefcase />}
                          >
                            Add Job
                          </Button>
                        </HStack>
                      </Box>
                    )}
                  </CardBody>
                </Card>

                {/* Recent Jobs */}
                <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
                  <CardHeader>
                    <Flex justify="space-between" align="center">
                      <Heading size="md">Recent Job Applications</Heading>
                      <Link as={RouterLink} to="/jobs" color="blue.500" fontSize="sm" fontWeight="medium">
                        View all <FiArrowRight style={{ display: 'inline', marginLeft: '4px' }} />
                      </Link>
                    </Flex>
                  </CardHeader>
                  <CardBody>
                    {paginatedJobs.length > 0 ? (
                      <VStack spacing={4} align="stretch">
                        {paginatedJobs.map((job) => (
                          <Box 
                            key={job.id} 
                            p={4} 
                            bg={useColorModeValue("gray.50", "gray.700")} 
                            borderRadius="md"
                            borderWidth="1px"
                            borderColor={borderColor}
                            transition="all 0.2s"
                            _hover={{ transform: 'translateY(-2px)', boxShadow: 'md' }}
                          >
                            <HStack spacing={3} align="center">
                              <Box
                                p={2}
                                bg={useColorModeValue("blue.50", "blue.900")}
                                color={useColorModeValue("blue.500", "blue.200")}
                                borderRadius="md"
                              >
                                <FiBriefcase />
                              </Box>
                              <VStack spacing={1} align="start" flex={1}>
                                <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                                  {job.title}
                                </Text>
                                <Text fontSize="xs" color="gray.600">
                                  {job.company}
                                </Text>
                                <Badge 
                                  size="sm" 
                                  colorScheme={job.isCompleted ? "green" : "blue"} 
                                  mt={1}
                                  borderRadius="full"
                                  px={2}
                                >
                                  {job.isCompleted ? 'Completed' : 'In Progress'}
                                </Badge>
                              </VStack>
                            </HStack>
                          </Box>
                        ))}
                        {/* Jobs Pagination */}
                        {totalJobsPages > 1 && (
                          <Flex justify="center" mt={4}>
                            <HStack spacing={2}>
                              <Button
                                size="sm"
                                onClick={() => setJobsPage(prev => Math.max(prev - 1, 1))}
                                isDisabled={jobsPage === 1}
                              >
                                Previous
                              </Button>
                              <Text fontSize="sm" color="gray.500">
                                {jobsPage} of {totalJobsPages}
                              </Text>
                              <Button
                                size="sm"
                                onClick={() => setJobsPage(prev => Math.min(prev + 1, totalJobsPages))}
                                isDisabled={jobsPage === totalJobsPages}
                              >
                                Next
                              </Button>
                            </HStack>
                          </Flex>
                        )}
                      </VStack>
                    ) : (
                      <Box textAlign="center" py={8}>
                        <Text fontSize="sm" color="gray.500">
                          No job applications yet
                        </Text>
                      </Box>
                    )}
                  </CardBody>
                </Card>
              </VStack>
            </GridItem>

            {/* Right Sidebar Content */}
            <GridItem>
              <VStack spacing={6} align="stretch">
                {/* Recent Cover Letters */}
                <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
                  <CardHeader>
                    <Flex justify="space-between" align="center">
                      <Heading size="md">Recent Cover Letters</Heading>
                      <Link as={RouterLink} to="/cover-letters" color="blue.500" fontSize="sm" fontWeight="medium">
                        View all <FiArrowRight style={{ display: 'inline', marginLeft: '4px' }} />
                      </Link>
                    </Flex>
                  </CardHeader>
                  <CardBody>
                    {recentCoverLetters.length > 0 ? (
                      <VStack spacing={4} align="stretch">
                        {recentCoverLetters.map((coverLetter) => (
                          <Box 
                            key={coverLetter.id} 
                            p={4} 
                            bg={useColorModeValue("gray.50", "gray.700")} 
                            borderRadius="md"
                            borderWidth="1px"
                            borderColor={borderColor}
                            transition="all 0.2s"
                            _hover={{ transform: 'translateY(-2px)', boxShadow: 'md' }}
                          >
                            <HStack spacing={3} align="center">
                              <Box
                                p={2}
                                bg={useColorModeValue("blue.50", "blue.900")}
                                color={useColorModeValue("blue.500", "blue.200")}
                                borderRadius="md"
                              >
                                <FiFileText />
                              </Box>
                              <VStack spacing={1} align="start" flex={1}>
                                <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                                  {coverLetter.title || 'Untitled Cover Letter'}
                                </Text>
                                <Text fontSize="xs" color="gray.600">
                                  Created {new Date(coverLetter.createdAt).toLocaleDateString()}
                                </Text>
                              </VStack>
                              <Button
                                as={RouterLink}
                                to={`/cover-letters/${coverLetter.id}`}
                                size="xs"
                                variant="ghost"
                                colorScheme="blue"
                                borderRadius="full"
                              >
                                <FiEye />
                              </Button>
                            </HStack>
                          </Box>
                        ))}
                      </VStack>
                    ) : (
                      <Box textAlign="center" py={6}>
                        <Text fontSize="sm" color="gray.500">
                          No cover letters yet
                        </Text>
                        <Button
                          as={RouterLink}
                          to="/cover-letters/new"
                          size="sm"
                          colorScheme="blue"
                          variant="outline"
                          mt={4}
                          leftIcon={<FiPlus />}
                        >
                          Create Cover Letter
                        </Button>
                      </Box>
                    )}
                  </CardBody>
                </Card>

                {/* Upgrade Notice */}
                {!user?.hasPaid && (
                  <Card bg="gradient-to-r" bgGradient="linear(to-r, blue.400, purple.500)" color="white">
                    <CardBody textAlign="center">
                      <Heading size="sm" mb={2}>Upgrade to Premium</Heading>
                      <Text fontSize="sm" mb={4}>
                        Get unlimited cover letters, advanced features, and priority support
                      </Text>
                      <Button
                        as={RouterLink}
                        to="/checkout"
                        colorScheme="whiteAlpha"
                        size="sm"
                      >
                        Upgrade Now
                      </Button>
                    </CardBody>
                  </Card>
                )}
              </VStack>
            </GridItem>
          </Grid>
        </VStack>
      </Container>
    </Box>
  );
};

export default Dashboard;