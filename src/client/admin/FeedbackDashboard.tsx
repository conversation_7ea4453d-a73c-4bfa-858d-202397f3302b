import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  Select,
  Input,
  Textarea,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Image,
  useColorModeValue,
  Flex,
  IconButton,
  Tooltip,
  Divider,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Avatar,
  Heading,
  Stack,
} from '@chakra-ui/react';
import { FaEye, FaEdit, FaCheck, FaTimes, FaUser, FaEnvelope, FaClock, FaImage, FaArrowLeft } from 'react-icons/fa';
import { useQuery, useAction } from 'wasp/client/operations';
import { getAllFeedback, getFeedbackById, updateFeedbackStatus } from 'wasp/client/operations';

interface FeedbackItem {
  id: string;
  type: string;
  description: string;
  rating?: number;
  status: string;
  priority: string;
  guestEmail?: string;
  screenshot?: string;
  url?: string;
  userAgent?: string;
  adminNotes?: string;
  assignedTo?: string;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    email: string;
    username: string;
  };
}

const FeedbackDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    page: 1
  });
  const [adminNotes, setAdminNotes] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [newPriority, setNewPriority] = useState('');

  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  // Color scheme
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');

  // Fetch feedback data
  const { data: feedbackData, isLoading, error, refetch } = useQuery(getAllFeedback, filters, {
    enabled: true,
    retry: 3,
    retryDelay: 1000,
  });
  const updateFeedbackAction = useAction(updateFeedbackStatus);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'red';
      case 'in_progress': return 'yellow';
      case 'resolved': return 'green';
      case 'closed': return 'gray';
      default: return 'gray';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'green';
      default: return 'gray';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'rating': return '⭐';
      case 'bug': return '🐛';
      case 'feature': return '✨';
      case 'improvement': return '🔧';
      case 'general': return '💬';
      default: return '📝';
    }
  };

  const handleViewFeedback = (feedback: FeedbackItem) => {
    setSelectedFeedback(feedback);
    setAdminNotes(feedback.adminNotes || '');
    setNewStatus(feedback.status);
    setNewPriority(feedback.priority);
    onOpen();
  };

  const handleUpdateFeedback = async () => {
    if (!selectedFeedback) return;

    try {
      await updateFeedbackAction({
        id: selectedFeedback.id,
        status: newStatus,
        adminNotes: adminNotes,
        priority: newPriority
      });

      toast({
        title: 'Feedback updated',
        description: 'Feedback status and notes have been updated successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      refetch();
      onClose();
    } catch (error) {
      console.error('Error updating feedback:', error);
      toast({
        title: 'Update failed',
        description: 'Failed to update feedback. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getUserDisplay = (feedback: FeedbackItem) => {
    if (feedback.user) {
      return `${feedback.user.username} (${feedback.user.email})`;
    }
    return feedback.guestEmail || 'Anonymous';
  };

  if (isLoading) {
    return (
      <Flex justify="center" align="center" h="400px">
        <Spinner size="xl" color="blue.500" />
      </Flex>
    );
  }

  if (error) {
    return (
      <Box p={6}>
        <Alert status="error" borderRadius="lg">
          <AlertIcon />
          <VStack align="start" spacing={2}>
            <Text fontWeight="bold">Failed to load feedback data</Text>
            <Text fontSize="sm">
              {error.message || 'This might be because the database migration hasn\'t been run yet.'}
            </Text>
            <Button size="sm" colorScheme="red" variant="outline" onClick={() => refetch()}>
              Try Again
            </Button>
          </VStack>
        </Alert>
      </Box>
    );
  }

  return (
    <Box p={6}>
      <VStack spacing={6} align="stretch">
        {/* Header with Back Button */}
        <HStack spacing={4} align="center">
          <IconButton
            aria-label="Go back"
            icon={<FaArrowLeft />}
            size="sm"
            variant="ghost"
            onClick={() => navigate('/admin')}
          />
          <Box flex={1}>
            <Text fontSize="2xl" fontWeight="bold" mb={1}>
              📧 Feedback Dashboard
            </Text>
            <Text color={textColor} fontSize="sm">
              Manage user feedback and support requests
            </Text>
          </Box>
        </HStack>

        {/* Filters */}
        <HStack spacing={4} wrap="wrap">
          <Select
            placeholder="All Statuses"
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            maxW="200px"
          >
            <option value="new">New</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </Select>

          <Select
            placeholder="All Types"
            value={filters.type}
            onChange={(e) => setFilters({ ...filters, type: e.target.value })}
            maxW="200px"
          >
            <option value="rating">Rating</option>
            <option value="bug">Bug Report</option>
            <option value="feature">Feature Request</option>
            <option value="improvement">Improvement</option>
            <option value="general">General</option>
            <option value="other">Other</option>
          </Select>

          <Button onClick={() => refetch()} colorScheme="blue" size="sm">
            Refresh
          </Button>
        </HStack>

        {/* Feedback Inbox - Email-style List */}
        <Card bg={bgColor} borderRadius="lg" border="1px" borderColor={borderColor} overflow="hidden">
          <VStack spacing={0} align="stretch">
            {feedbackData?.feedback?.map((feedback: FeedbackItem, index) => (
              <Box
                key={feedback.id}
                p={4}
                borderBottom={index < feedbackData.feedback.length - 1 ? "1px" : "none"}
                borderColor={borderColor}
                _hover={{
                  bg: hoverBg,
                  cursor: 'pointer'
                }}
                transition="background 0.2s"
                onClick={() => handleViewFeedback(feedback)}
              >
                <Flex align="center" justify="space-between">
                  {/* Left side - User info and content */}
                  <Flex align="center" spacing={0} flex={1} minW={0}>
                    <Avatar
                      size="sm"
                      name={getUserDisplay(feedback)}
                      bg={feedback.user ? 'green.500' : 'gray.500'}
                      color="white"
                      mr={3}
                    />

                    <Box flex={1} minW={0}>
                      {/* First row - User name and subject */}
                      <Flex align="center" justify="space-between" mb={1}>
                        <HStack spacing={2} minW={0} flex={1}>
                          <Text fontSize="sm" fontWeight="semibold" noOfLines={1}>
                            {getUserDisplay(feedback)}
                          </Text>
                          <Badge
                            colorScheme={feedback.user ? 'green' : 'gray'}
                            size="sm"
                            fontSize="xs"
                          >
                            {feedback.user ? 'User' : 'Guest'}
                          </Badge>
                          <Text fontSize="lg" mx={2}>{getTypeIcon(feedback.type)}</Text>
                          <Text fontSize="sm" fontWeight="medium" textTransform="capitalize" color="gray.600">
                            {feedback.type}
                          </Text>
                          {feedback.rating && (
                            <HStack spacing={1}>
                              <Text fontSize="sm" fontWeight="bold">{feedback.rating}</Text>
                              <Text fontSize="xs" color="yellow.500">⭐</Text>
                            </HStack>
                          )}
                        </HStack>

                        {/* Right side - Status, priority, and date */}
                        <HStack spacing={2} flexShrink={0}>
                          <Badge
                            colorScheme={getStatusColor(feedback.status)}
                            textTransform="capitalize"
                            fontSize="xs"
                          >
                            {feedback.status.replace('_', ' ')}
                          </Badge>
                          <Badge
                            colorScheme={getPriorityColor(feedback.priority)}
                            textTransform="capitalize"
                            fontSize="xs"
                          >
                            {feedback.priority}
                          </Badge>
                          {feedback.screenshot && (
                            <FaImage size="12px" color="gray" />
                          )}
                          <Text fontSize="xs" color="gray.500" minW="fit-content">
                            {formatDate(feedback.createdAt)}
                          </Text>
                        </HStack>
                      </Flex>

                      {/* Second row - Description preview */}
                      <Text fontSize="sm" color="gray.600" noOfLines={1} pr={4}>
                        {feedback.description}
                      </Text>
                    </Box>
                  </Flex>
                </Flex>
              </Box>
            ))}
          </VStack>
        </Card>

        {/* Empty State */}
        {feedbackData?.feedback?.length === 0 && (
          <Box textAlign="center" py={10}>
            <Text fontSize="lg" color="gray.500" mb={2}>📭 No feedback found</Text>
            <Text fontSize="sm" color="gray.400">
              Try adjusting your filters or check back later.
            </Text>
          </Box>
        )}

        {/* Pagination */}
        {feedbackData?.pagination && (
          <HStack justify="center" spacing={2}>
            <Text fontSize="sm" color={textColor}>
              Page {feedbackData.pagination.page} of {feedbackData.pagination.totalPages}
              ({feedbackData.pagination.total} total)
            </Text>
          </HStack>
        )}
      </VStack>

      {/* Feedback Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent maxH="90vh" overflowY="auto">
          <ModalHeader>
            <HStack>
              <Text fontSize="lg">{getTypeIcon(selectedFeedback?.type || '')}</Text>
              <Text>Feedback Details</Text>
              <Badge colorScheme={getStatusColor(selectedFeedback?.status || '')}>
                {selectedFeedback?.status?.replace('_', ' ')}
              </Badge>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          
          <ModalBody>
            {selectedFeedback && (
              <VStack spacing={4} align="stretch">
                {/* User Information */}
                <Box>
                  <Text fontWeight="bold" mb={2}>👤 User Information</Text>
                  <VStack align="start" spacing={1} pl={4}>
                    <Text><strong>User:</strong> {getUserDisplay(selectedFeedback)}</Text>
                    <Text><strong>Type:</strong> {selectedFeedback.user ? 'Registered User' : 'Guest User'}</Text>
                    <Text><strong>Submitted:</strong> {formatDate(selectedFeedback.createdAt)}</Text>
                  </VStack>
                </Box>

                <Divider />

                {/* Feedback Content */}
                <Box>
                  <Text fontWeight="bold" mb={2}>📝 Feedback Content</Text>
                  <VStack align="start" spacing={2} pl={4}>
                    <Text><strong>Type:</strong> {selectedFeedback.type}</Text>
                    {selectedFeedback.rating && (
                      <Text>
                        <strong>Rating:</strong> {selectedFeedback.rating}/5 ⭐
                        <Text as="span" ml={2} fontSize="sm" color="gray.600">
                          ({selectedFeedback.rating === 1 && "Poor"}
                          {selectedFeedback.rating === 2 && "Fair"}
                          {selectedFeedback.rating === 3 && "Good"}
                          {selectedFeedback.rating === 4 && "Very Good"}
                          {selectedFeedback.rating === 5 && "Excellent"})
                        </Text>
                      </Text>
                    )}
                    <Box>
                      <Text fontWeight="medium" mb={1}>Description:</Text>
                      <Text bg="gray.50" p={3} borderRadius="md" whiteSpace="pre-wrap">
                        {selectedFeedback.description}
                      </Text>
                    </Box>
                  </VStack>
                </Box>

                {/* Screenshot */}
                {selectedFeedback.screenshot && (
                  <>
                    <Divider />
                    <Box>
                      <Text fontWeight="bold" mb={2}>📸 Screenshot</Text>
                      <Image
                        src={selectedFeedback.screenshot}
                        alt="User screenshot"
                        maxH="300px"
                        borderRadius="md"
                        border="1px"
                        borderColor={borderColor}
                      />
                    </Box>
                  </>
                )}

                <Divider />

                {/* Technical Information */}
                <Box>
                  <Text fontWeight="bold" mb={2}>🌐 Technical Information</Text>
                  <VStack align="start" spacing={1} pl={4}>
                    <Text><strong>URL:</strong> {selectedFeedback.url}</Text>
                    <Text><strong>User Agent:</strong> <Text as="span" fontSize="sm" fontFamily="mono">{selectedFeedback.userAgent}</Text></Text>
                  </VStack>
                </Box>

                <Divider />

                {/* Admin Controls */}
                <Box>
                  <Text fontWeight="bold" mb={3}>⚙️ Admin Controls</Text>
                  <VStack spacing={3} align="stretch">
                    <HStack>
                      <Select
                        value={newStatus}
                        onChange={(e) => setNewStatus(e.target.value)}
                        placeholder="Select Status"
                      >
                        <option value="new">New</option>
                        <option value="in_progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                        <option value="closed">Closed</option>
                      </Select>
                      <Select
                        value={newPriority}
                        onChange={(e) => setNewPriority(e.target.value)}
                        placeholder="Select Priority"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </Select>
                    </HStack>
                    <Textarea
                      placeholder="Add admin notes..."
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      rows={4}
                    />
                  </VStack>
                </Box>
              </VStack>
            )}
          </ModalBody>

          <ModalFooter>
            <HStack spacing={3}>
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button colorScheme="blue" onClick={handleUpdateFeedback}>
                Update Feedback
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default FeedbackDashboard;
