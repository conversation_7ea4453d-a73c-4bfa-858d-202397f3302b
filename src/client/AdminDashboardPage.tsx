import React, { useState, useEffect, useRef, Suspense } from 'react';
import {
  Box,
  Flex,
  Grid,
  GridItem,
  Heading,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useColorModeValue,
  Alert,
  AlertIcon,
  Spinner,
  Center,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  useToast,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Textarea,
  Switch,
  FormControl,
  FormLabel,
  Divider,
  SimpleGrid,
  Progress,
  Tooltip,
  Container,
  Avatar,
  AvatarGroup,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Icon,
  useBreakpointValue,
  Drawer,
  DrawerBody,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
} from '@chakra-ui/react';
import { 
  FiUsers, 
  FiFileText, 
  FiBriefcase, 
  FiDollarSign, 
  FiSearch, 
  FiRefreshCw,
  FiSettings,
  FiShield,
  FiActivity,
  FiBarChart,
  FiMonitor,
  FiDatabase,
  FiMail,
  FiDownload,
  FiMoreVertical,
  FiUserPlus,
  FiUserMinus,
  FiEdit,
  FiTrash2,
  FiEye,
  FiChevronRight,
  FiHome,
  FiServer,
  FiGlobe,
  FiFilter,
  FiPlus,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiClock,
  FiTrendingUp,
  FiTrendingDown,
  FiMenu,
  FiAlertTriangle,
  FiBell,
} from 'react-icons/fi';
import { 
  MdError, 
  MdTrendingUp, 
  MdTrendingDown, 
  MdWarning, 
  MdCheckCircle,
  MdNotifications,
  MdSecurity,
} from 'react-icons/md';

import { getAdminAnalytics, getSystemMetrics, getUserActivity, getErrorLogs, getAllUsers, getLighthouseMetrics, updateUserDetails, setUserVerified, sendUserEmail, adminDeleteUserAccount, getUnreadFeedbackCount } from 'wasp/client/operations';
import { useQuery } from 'wasp/client/operations';
import BackButton from './components/BackButton';

interface AdminData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    paidUsers: number;
    totalCoverLetters: number;
    totalJobs: number;
    totalResumes: number;
    conversionRate: string;
  };
  growth: {
    newUsersThisWeek: number;
    coverLettersThisWeek: number;
    userGrowthRate: string;
    revenueGrowthRate: string;
  };
  recentUsers: any[];
  timestamp: Date;
}

// Sidebar navigation items
const navItems = [
  { id: 'dashboard', label: 'Dashboard', icon: FiHome },
  { id: 'analytics', label: 'Analytics', icon: FiBarChart },
  { id: 'users', label: 'User Management', icon: FiUsers },
  { id: 'feedback', label: 'Feedback', icon: FiMail, href: '/admin/feedback' },
  { id: 'content', label: 'Content Overview', icon: FiFileText },
  { id: 'system', label: 'System Health', icon: FiServer },
  { id: 'performance', label: 'Performance', icon: FiMonitor },
  { id: 'security', label: 'Security & Logs', icon: FiShield },
  { id: 'settings', label: 'Settings', icon: FiSettings },
];

const AdminDashboard: React.FC = () => {
  const [adminData, setAdminData] = useState<AdminData | null>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [userActivity, setUserActivity] = useState<any[]>([]);
  const [errorLogs, setErrorLogs] = useState<any[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<any>(null);
  const [lighthouseMetrics, setLighthouseMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [userSearch, setUserSearch] = useState('');
  const [errorSeverityFilter, setErrorSeverityFilter] = useState('');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [lighthouseLoading, setLighthouseLoading] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [emailData, setEmailData] = useState({ subject: '', message: '', emailType: 'general' });
  const [deleteConfirmation, setDeleteConfirmation] = useState({ show: false, userId: null, username: '' });
  const [actionLoading, setActionLoading] = useState(false);
  const [notifications] = useState([
    { id: 1, type: 'warning', message: 'High error rate detected in the last hour', time: '5 min ago' },
    { id: 2, type: 'info', message: 'System maintenance scheduled for tonight', time: '2 hours ago' },
    { id: 3, type: 'success', message: 'New user milestone reached: 1000+ users', time: '1 day ago' },
  ]);
  
  const { isOpen: isUserModalOpen, onOpen: onUserModalOpen, onClose: onUserModalClose } = useDisclosure();
  const { isOpen: isSettingsModalOpen, onOpen: onSettingsModalOpen, onClose: onSettingsModalClose } = useDisclosure();
  const { isOpen: isEditUserModalOpen, onOpen: onEditUserModalOpen, onClose: onEditUserModalClose } = useDisclosure();
  const { isOpen: isEmailModalOpen, onOpen: onEmailModalOpen, onClose: onEmailModalClose } = useDisclosure();
  const { isOpen: isDeleteModalOpen, onOpen: onDeleteModalOpen, onClose: onDeleteModalClose } = useDisclosure();
  const { isOpen: isMobileNavOpen, onOpen: onMobileNavOpen, onClose: onMobileNavClose } = useDisclosure();

  // Responsive breakpoint values
  const isMobile = useBreakpointValue({ base: true, md: false });
  const isTablet = useBreakpointValue({ base: false, md: true, lg: false });
  const headerPadding = useBreakpointValue({ base: 2, md: 4, lg: 6 });
  const contentPadding = useBreakpointValue({ base: 4, md: 6, lg: 8 });

  // Color mode values
  const sidebarBg = useColorModeValue('white', 'gray.800');
  const mainBg = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headerBg = useColorModeValue('white', 'gray.800');
  const hoverBg = useColorModeValue('gray.100', 'gray.700');
  const activeBg = useColorModeValue('blue.50', 'blue.900');
  const activeColor = useColorModeValue('blue.600', 'blue.200');
  const toast = useToast();

  // Get unread feedback count
  const { data: feedbackCountData } = useQuery(getUnreadFeedbackCount, {}, {
    refetchInterval: 30000, // Refetch every 30 seconds
    enabled: true,
  });

  const unreadFeedbackCount = feedbackCountData?.count || 0;

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Auto-refresh lighthouse data every 5 minutes when on performance section
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout;
    
    if (activeSection === 'performance' && lighthouseMetrics?.available) {
      refreshInterval = setInterval(() => {
        console.log('Auto-refreshing lighthouse data...');
        refreshLighthouseData();
      }, 5 * 60 * 1000); // 5 minutes
    }
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [activeSection, lighthouseMetrics?.available]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [analytics, usersData, activity, errors, metrics, lighthouse] = await Promise.all([
        getAdminAnalytics(),
        getAllUsers({ page: 1, limit: 50 }),
        getUserActivity({ limit: 100 }),
        getErrorLogs({ limit: 100 }),
        getSystemMetrics({ timeframe: '24h' }),
        getLighthouseMetrics(),
      ]);

      setAdminData(analytics);
      setUsers(usersData.users);
      setUserActivity(activity);
      setErrorLogs(errors);
      setSystemMetrics(metrics);
      setLighthouseMetrics(lighthouse);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard data. You may not have admin privileges.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Separate function to refresh lighthouse data independently
  const refreshLighthouseData = async () => {
    try {
      setLighthouseLoading(true);
      const lighthouse = await getLighthouseMetrics();
      setLighthouseMetrics(lighthouse);
      
      toast({
        title: 'Success',
        description: 'Lighthouse data refreshed successfully!',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error refreshing lighthouse data:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh lighthouse data. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLighthouseLoading(false);
    }
  };

  // User Management Action Handlers
  const handleEditUser = (user: any) => {
    setEditingUser(user);
    onEditUserModalOpen();
  };

  const handleSendEmail = (user: any) => {
    setSelectedUser(user);
    setEmailData({ subject: '', message: '', emailType: 'general' });
    onEmailModalOpen();
  };

  const handleDeleteUser = (user: any) => {
    setDeleteConfirmation({ 
      show: true, 
      userId: user.id, 
      username: user.username 
    });
    onDeleteModalOpen();
  };

  const handleSetVerified = async (user: any, verified: boolean) => {
    try {
      setActionLoading(true);
      await setUserVerified({ userId: user.id, verified });
      
      // Refresh users list
      const usersData = await getAllUsers({ page: 1, limit: 50 });
      setUsers(usersData.users);
      
      toast({
        title: 'Success',
        description: `User ${verified ? 'verified' : 'unverified'} successfully!`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error setting user verification:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user verification status',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setActionLoading(false);
    }
  };

  const submitUserUpdate = async (formData: any) => {
    if (!editingUser) return;
    
    try {
      setActionLoading(true);
      await updateUserDetails({
        userId: editingUser.id,
        updates: formData,
      });
      
      // Refresh users list
      const usersData = await getAllUsers({ page: 1, limit: 50 });
      setUsers(usersData.users);
      
      onEditUserModalClose();
      toast({
        title: 'Success',
        description: 'User updated successfully!',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setActionLoading(false);
    }
  };

  const submitSendEmail = async () => {
    if (!selectedUser) return;
    
    try {
      setActionLoading(true);
      await sendUserEmail({
        userId: selectedUser.id,
        subject: emailData.subject,
        message: emailData.message,
        emailType: emailData.emailType,
      });
      
      onEmailModalClose();
      setEmailData({ subject: '', message: '', emailType: 'general' });
      
      toast({
        title: 'Success',
        description: `Email sent to ${selectedUser.email}!`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: 'Error',
        description: 'Failed to send email',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setActionLoading(false);
    }
  };

  const confirmDeleteUser = async (deleteAllData = false) => {
    if (!deleteConfirmation.userId) return;
    
    try {
      setActionLoading(true);
      await adminDeleteUserAccount({
        userId: deleteConfirmation.userId,
        reason: 'Admin deletion',
        deleteAllData,
      });
      
      // Refresh users list
      const usersData = await getAllUsers({ page: 1, limit: 50 });
      setUsers(usersData.users);
      
      onDeleteModalClose();
      setDeleteConfirmation({ show: false, userId: null, username: '' });
      
      toast({
        title: 'Success',
        description: deleteAllData ? 'User permanently deleted!' : 'User account deactivated!',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete user account',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Chart data configurations
  const userGrowthData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Active Users',
        data: [120, 190, 150, 250, 220, 300],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4,
      },
      {
        label: 'New Signups',
        data: [80, 120, 100, 180, 160, 220],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const revenueData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [
      {
        label: 'Revenue ($)',
        data: [1200, 1900, 1500, 2100],
        backgroundColor: 'rgba(139, 92, 246, 0.8)',
        borderColor: 'rgb(139, 92, 246)',
        borderWidth: 1,
      },
    ],
  };

  const conversionData = adminData ? {
    labels: ['Free Users', 'Paid Users'],
    datasets: [
      {
        data: [adminData.overview.totalUsers - adminData.overview.paidUsers, adminData.overview.paidUsers],
        backgroundColor: ['#E2E8F0', '#3182CE'],
        borderWidth: 0,
      },
    ],
  } : { labels: [], datasets: [] };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Render functions for each section
  const renderActiveSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return renderDashboard();
      case 'analytics':
        return renderAnalytics();
      case 'users':
        return renderUserManagement();
      case 'content':
        return renderContentOverview();
      case 'system':
        return renderSystemHealth();
      case 'performance':
        return renderPerformance();
      case 'security':
        return renderSecurity();
      case 'settings':
        return renderSettings();
      default:
        return renderDashboard();
    }
  };

  const renderDashboard = () => (
    <VStack spacing={6} align="stretch">
      {/* Welcome Header */}
      <Box>
        <Heading size="md" mb={1}>Welcome back, Admin</Heading>
        <Text color="gray.600" fontSize="sm">Here's what's happening with your platform today</Text>
      </Box>

      {/* Key Metrics Grid - Enhanced for wider layout */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4, xl: 6 }} spacing={4}>
        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
          <CardBody>
            <Flex align="center" justify="space-between">
              <VStack spacing={1} align="start">
                <Text fontSize="xs" color="gray.600" fontWeight="medium">Total Users</Text>
                <Text fontSize="xl" fontWeight="bold" color="blue.500">
                  {adminData?.overview.totalUsers.toLocaleString()}
                </Text>
                <HStack spacing={1}>
                  <FiTrendingUp color="green" size={12} />
                  <Text fontSize="xs" color="green.500" fontWeight="medium">+{adminData?.growth.userGrowthRate}</Text>
                </HStack>
              </VStack>
              <Box p={2} bg="blue.50" borderRadius="lg">
                <FiUsers size={20} color="blue" />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
          <CardBody>
            <Flex align="center" justify="space-between">
              <VStack spacing={1} align="start">
                <Text fontSize="xs" color="gray.600" fontWeight="medium">Active Users</Text>
                <Text fontSize="xl" fontWeight="bold" color="green.500">
                  {adminData?.overview.activeUsers.toLocaleString()}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {adminData ? ((adminData.overview.activeUsers / adminData.overview.totalUsers) * 100).toFixed(1) : '0'}% of total
                </Text>
              </VStack>
              <Box p={2} bg="green.50" borderRadius="lg">
                <FiActivity size={20} color="green" />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
          <CardBody>
            <Flex align="center" justify="space-between">
              <VStack spacing={1} align="start">
                <Text fontSize="xs" color="gray.600" fontWeight="medium">Revenue</Text>
                <Text fontSize="xl" fontWeight="bold" color="purple.500">
                  ${adminData ? (adminData.overview.paidUsers * 9.99).toFixed(0) : '0'}
                </Text>
                <HStack spacing={1}>
                  <FiTrendingUp color="green" size={12} />
                  <Text fontSize="xs" color="green.500" fontWeight="medium">+{adminData?.growth.revenueGrowthRate}</Text>
                </HStack>
              </VStack>
              <Box p={2} bg="purple.50" borderRadius="lg">
                <FiDollarSign size={20} color="purple" />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
          <CardBody>
            <Flex align="center" justify="space-between">
              <VStack spacing={1} align="start">
                <Text fontSize="xs" color="gray.600" fontWeight="medium">Conversion Rate</Text>
                <Text fontSize="xl" fontWeight="bold" color="orange.500">
                  {adminData?.overview.conversionRate}%
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {adminData?.overview.paidUsers} paid users
                </Text>
              </VStack>
              <Box p={2} bg="orange.50" borderRadius="lg">
                <FiBarChart size={20} color="orange" />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
          <CardBody>
            <Flex align="center" justify="space-between">
              <VStack spacing={1} align="start">
                <Text fontSize="xs" color="gray.600" fontWeight="medium">Cover Letters</Text>
                <Text fontSize="xl" fontWeight="bold" color="blue.600">
                  {adminData?.overview.totalCoverLetters}
                </Text>
                <Text fontSize="xs" color="green.500" fontWeight="medium">
                  +{adminData?.growth.coverLettersThisWeek} this week
                </Text>
              </VStack>
              <Box p={2} bg="blue.50" borderRadius="lg">
                <FiFileText size={20} color="blue" />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm">
          <CardBody>
            <Flex align="center" justify="space-between">
              <VStack spacing={1} align="start">
                <Text fontSize="xs" color="gray.600" fontWeight="medium">Job Applications</Text>
                <Text fontSize="xl" fontWeight="bold" color="teal.500">
                  {adminData?.overview.totalJobs}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  Total tracked
                </Text>
              </VStack>
              <Box p={2} bg="teal.50" borderRadius="lg">
                <FiBriefcase size={20} color="teal" />
              </Box>
            </Flex>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Charts Section - Better organized for wide layout */}
      <SimpleGrid columns={{ base: 1, lg: 2, xl: 3 }} spacing={6}>
        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="md">
          <CardHeader>
            <Flex justify="space-between" align="center">
              <Heading size="sm">User Growth Trends</Heading>
              <Button size="xs" variant="outline">View Details</Button>
            </Flex>
          </CardHeader>
          <CardBody>
            <Box h="250px">
              <Text fontSize="sm">Chart loading failed</Text>
            </Box>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="md">
          <CardHeader>
            <Flex justify="space-between" align="center">
              <Heading size="sm">User Distribution</Heading>
              <Button size="xs" variant="outline">Export</Button>
            </Flex>
          </CardHeader>
          <CardBody>
            <Box h="250px">
              <Text fontSize="sm">Chart loading failed</Text>
            </Box>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="md">
          <CardHeader>
            <Flex justify="space-between" align="center">
              <Heading size="sm">Quick Actions</Heading>
            </Flex>
          </CardHeader>
          <CardBody>
            <VStack spacing={3} align="stretch">
              <Button leftIcon={<FiUserPlus />} colorScheme="blue" size="sm">
                Add New User
              </Button>
              <Button leftIcon={<FiDownload />} variant="outline" size="sm">
                Export Report
              </Button>
              <Button leftIcon={<FiMail />} colorScheme="green" size="sm">
                Send Broadcast
              </Button>
              <Button leftIcon={<FiSettings />} variant="outline" size="sm">
                System Settings
              </Button>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Recent Activity - Enhanced layout */}
      <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="md">
        <CardHeader>
          <Flex justify="space-between" align="center">
            <VStack spacing={1} align="start">
              <Heading size="sm">Recent Activity</Heading>
              <Text fontSize="xs" color="gray.600">Latest user actions and system events</Text>
            </VStack>
            <HStack>
              <Button size="xs" variant="ghost" rightIcon={<FiEye />}>
                View All
              </Button>
              <Button size="xs" leftIcon={<FiRefreshCw />} onClick={loadDashboardData}>
                Refresh
              </Button>
            </HStack>
          </Flex>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={3}>
            {userActivity.slice(0, 6).map((activity, index) => (
              <Box key={index} p={3} bg={hoverBg} borderRadius="md" border="1px" borderColor={borderColor}>
                <HStack spacing={2}>
                  <Avatar size="xs" name={`User ${activity.userId}`} />
                  <VStack spacing={0} align="start" flex={1}>
                    <Text fontSize="xs" fontWeight="medium" noOfLines={1}>
                      User performed {activity.action}
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      {activity.resource && `on ${activity.resource}`}
                    </Text>
                    <Text fontSize="xs" color="gray.400">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </Text>
                  </VStack>
                </HStack>
              </Box>
            ))}
          </SimpleGrid>
        </CardBody>
      </Card>
    </VStack>
  );

  const renderAnalytics = () => (
    <VStack spacing={6} align="stretch">
      {/* Analytics Header */}
      <Box>
        <Heading size="md" mb={1}>Analytics Dashboard</Heading>
        <Text color="gray.600" fontSize="sm">Comprehensive insights into your platform performance</Text>
      </Box>

      {/* Enhanced Charts Grid */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="md">
          <CardHeader>
            <VStack spacing={2} align="start">
              <Heading size="md">Revenue Trends</Heading>
              <Text fontSize="sm" color="gray.600">Weekly revenue performance</Text>
            </VStack>
          </CardHeader>
          <CardBody>
            <Box h="350px">
              <Text>Chart loading failed</Text>
            </Box>
          </CardBody>
        </Card>

        <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="md">
          <CardHeader>
            <VStack spacing={2} align="start">
              <Heading size="md">User Engagement</Heading>
              <Text fontSize="sm" color="gray.600">User activity and growth metrics</Text>
            </VStack>
          </CardHeader>
          <CardBody>
            <Box h="350px">
              <Text>Chart loading failed</Text>
            </Box>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Content Analytics - Enhanced Grid */}
      <Box>
        <Heading size="lg" mb={6}>Content Analytics</Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm" _hover={{ shadow: "md", transform: "translateY(-2px)" }} transition="all 0.2s">
            <CardBody textAlign="center" py={8}>
              <Box p={4} bg="blue.50" borderRadius="full" w="fit-content" mx="auto" mb={4}>
                <FiFileText size={32} color="blue" />
              </Box>
              <Text fontSize="3xl" fontWeight="bold" mb={2} color="blue.500">
                {adminData?.overview.totalCoverLetters}
              </Text>
              <Text fontSize="lg" fontWeight="medium" mb={1}>Cover Letters Created</Text>
              <Text fontSize="sm" color="green.500" fontWeight="medium">
                +{adminData?.growth.coverLettersThisWeek} this week
              </Text>
            </CardBody>
          </Card>

          <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm" _hover={{ shadow: "md", transform: "translateY(-2px)" }} transition="all 0.2s">
            <CardBody textAlign="center" py={8}>
              <Box p={4} bg="green.50" borderRadius="full" w="fit-content" mx="auto" mb={4}>
                <FiBriefcase size={32} color="green" />
              </Box>
              <Text fontSize="3xl" fontWeight="bold" mb={2} color="green.500">
                {adminData?.overview.totalJobs}
              </Text>
              <Text fontSize="lg" fontWeight="medium" mb={1}>Job Applications</Text>
              <Text fontSize="sm" color="gray.500">Tracked applications</Text>
            </CardBody>
          </Card>

          <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm" _hover={{ shadow: "md", transform: "translateY(-2px)" }} transition="all 0.2s">
            <CardBody textAlign="center" py={8}>
              <Box p={4} bg="purple.50" borderRadius="full" w="fit-content" mx="auto" mb={4}>
                <FiFileText size={32} color="purple" />
              </Box>
              <Text fontSize="3xl" fontWeight="bold" mb={2} color="purple.500">
                {adminData?.overview.totalResumes}
              </Text>
              <Text fontSize="lg" fontWeight="medium" mb={1}>Resumes Generated</Text>
              <Text fontSize="sm" color="gray.500">Total created</Text>
            </CardBody>
          </Card>

          <Card bg={cardBg} border="1px" borderColor={borderColor} shadow="sm" _hover={{ shadow: "md", transform: "translateY(-2px)" }} transition="all 0.2s">
            <CardBody textAlign="center" py={8}>
              <Box p={4} bg="orange.50" borderRadius="full" w="fit-content" mx="auto" mb={4}>
                <FiUsers size={32} color="orange" />
              </Box>
              <Text fontSize="3xl" fontWeight="bold" mb={2} color="orange.500">
                {adminData?.overview.activeUsers}
              </Text>
              <Text fontSize="lg" fontWeight="medium" mb={1}>Active Users</Text>
              <Text fontSize="sm" color="gray.500">Last 30 days</Text>
            </CardBody>
          </Card>
        </SimpleGrid>
      </Box>
    </VStack>
  );

  const renderUserManagement = () => (
    <VStack spacing={4} align="stretch">
      {/* Header with actions */}
      <Card bg={cardBg} border="1px" borderColor={borderColor}>
        <CardBody>
          <Flex justify="space-between" align="center" mb={3}>
            <Heading size="sm">User Management</Heading>
            <HStack>
              <Button leftIcon={<FiUserPlus />} colorScheme="blue" size="xs" onClick={onUserModalOpen}>
                Add User
              </Button>
              <Button leftIcon={<FiDownload />} variant="outline" size="xs">
                Export Users
              </Button>
            </HStack>
          </Flex>

          {/* Search and filters */}
          <HStack spacing={3} mb={3}>
            <InputGroup maxW="250px">
              <InputLeftElement pointerEvents="none">
                <FiSearch color="gray.300" size={14} />
              </InputLeftElement>
              <Input
                placeholder="Search users..."
                value={userSearch}
                onChange={(e) => setUserSearch(e.target.value)}
                size="sm"
                fontSize="sm"
              />
            </InputGroup>
            <Select placeholder="Filter by status" maxW="150px" size="sm" fontSize="sm">
              <option value="all">All Users</option>
              <option value="paid">Paid Users</option>
              <option value="free">Free Users</option>
              <option value="active">Active Users</option>
            </Select>
          </HStack>

          {/* Users table */}
          <TableContainer>
            <Table size="sm">
              <Thead>
                <Tr>
                  <Th fontSize="xs">User</Th>
                  <Th fontSize="xs">Status</Th>
                  <Th fontSize="xs">Credits</Th>
                  <Th fontSize="xs">Last Active</Th>
                  <Th fontSize="xs">Joined</Th>
                  <Th fontSize="xs">Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {users
                  .filter(user => 
                    !userSearch || 
                    user.username.toLowerCase().includes(userSearch.toLowerCase()) ||
                    user.email.toLowerCase().includes(userSearch.toLowerCase())
                  )
                  .map((user) => (
                  <Tr key={user.id}>
                    <Td>
                      <HStack spacing={2}>
                        <Avatar size="xs" name={user.username} />
                        <VStack spacing={0} align="start">
                          <HStack spacing={1}>
                            <Text fontWeight="medium" fontSize="sm">{user.username}</Text>
                            {user.bio?.includes('[VERIFIED]') && (
                              <Badge colorScheme="green" size="xs">✓</Badge>
                            )}
                          </HStack>
                          <Text fontSize="xs" color="gray.500">{user.email}</Text>
                        </VStack>
                      </HStack>
                    </Td>
                    <Td>
                      <VStack spacing={1} align="start">
                        <Badge colorScheme={user.hasPaid ? 'green' : 'gray'} fontSize="xs">
                          {user.hasPaid ? 'Premium' : 'Free'}
                        </Badge>
                        {user.bio?.includes('[VERIFIED]') && (
                          <Badge colorScheme="blue" fontSize="xs" variant="outline">
                            Verified
                          </Badge>
                        )}
                      </VStack>
                    </Td>
                    <Td fontSize="sm">{user.credits}</Td>
                    <Td fontSize="sm">
                      {user.lastLoginAt 
                        ? new Date(user.lastLoginAt).toLocaleDateString()
                        : 'Never'
                      }
                    </Td>
                    <Td fontSize="sm">{new Date(user.createdAt).toLocaleDateString()}</Td>
                    <Td>
                      <Menu>
                        <MenuButton 
                          as={IconButton} 
                          icon={<FiMoreVertical />} 
                          size="xs" 
                          variant="ghost" 
                          aria-label="User actions menu"
                          isLoading={actionLoading}
                        />
                        <MenuList>
                          <MenuItem icon={<FiEye />} onClick={() => {
                            setSelectedUser(user);
                            onUserModalOpen();
                          }} fontSize="sm">
                            View Details
                          </MenuItem>
                          <MenuItem icon={<FiEdit />} onClick={() => handleEditUser(user)} fontSize="sm">
                            Edit User
                          </MenuItem>
                          <MenuItem icon={<FiMail />} onClick={() => handleSendEmail(user)} fontSize="sm">
                            Send Email
                          </MenuItem>
                          <Divider />
                          {user.bio?.includes('[VERIFIED]') ? (
                            <MenuItem 
                              icon={<FiShield />} 
                              onClick={() => handleSetVerified(user, false)} 
                              fontSize="sm"
                              color="orange.500"
                            >
                              Remove Verification
                            </MenuItem>
                          ) : (
                            <MenuItem 
                              icon={<FiShield />} 
                              onClick={() => handleSetVerified(user, true)} 
                              fontSize="sm"
                              color="green.500"
                            >
                              Set as Verified
                            </MenuItem>
                          )}
                          <Divider />
                          <MenuItem 
                            icon={<FiTrash2 />} 
                            color="red.500" 
                            fontSize="sm"
                            onClick={() => handleDeleteUser(user)}
                          >
                            Delete User
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        </CardBody>
      </Card>
    </VStack>
  );

  const renderContentOverview = () => (
    <VStack spacing={4} align="stretch">
      <Card bg={cardBg} border="1px" borderColor={borderColor}>
        <CardHeader>
          <Heading size="sm">Content Overview</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
            <Box textAlign="center" p={3} border="1px" borderColor={borderColor} borderRadius="md">
              <FiFileText size={24} color="blue" />
              <Text fontSize="lg" fontWeight="bold" mt={1}>
                {adminData?.overview.totalCoverLetters}
              </Text>
              <Text fontSize="xs" color="gray.600">Cover Letters</Text>
            </Box>
            <Box textAlign="center" p={3} border="1px" borderColor={borderColor} borderRadius="md">
              <FiBriefcase size={24} color="green" />
              <Text fontSize="lg" fontWeight="bold" mt={1}>
                {adminData?.overview.totalJobs}
              </Text>
              <Text fontSize="xs" color="gray.600">Job Applications</Text>
            </Box>
            <Box textAlign="center" p={3} border="1px" borderColor={borderColor} borderRadius="md">
              <FiFileText size={24} color="purple" />
              <Text fontSize="lg" fontWeight="bold" mt={1}>
                {adminData?.overview.totalResumes}
              </Text>
              <Text fontSize="xs" color="gray.600">Resumes</Text>
            </Box>
            <Box textAlign="center" p={3} border="1px" borderColor={borderColor} borderRadius="md">
              <FiUsers size={24} color="orange" />
              <Text fontSize="lg" fontWeight="bold" mt={1}>
                {adminData?.overview.activeUsers}
              </Text>
              <Text fontSize="xs" color="gray.600">Active Users</Text>
            </Box>
          </SimpleGrid>
        </CardBody>
      </Card>
    </VStack>
  );

  const renderSystemHealth = () => (
    <VStack spacing={4} align="stretch">
      <Card bg={cardBg} border="1px" borderColor={borderColor}>
        <CardHeader>
          <Heading size="sm">System Health Monitor</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
            <Box textAlign="center" p={3} border="1px" borderColor="green.200" borderRadius="md" bg="green.50">
              <FiServer size={24} color="green" />
              <Text fontSize="md" fontWeight="bold" mt={1} color="green.600">
                API Server
              </Text>
              <Text fontSize="xs" color="green.600">Healthy</Text>
              <Progress value={88} colorScheme="green" size="sm" mt={2} />
            </Box>
            <Box textAlign="center" p={3} border="1px" borderColor="yellow.200" borderRadius="md" bg="yellow.50">
              <FiDatabase size={24} color="orange" />
              <Text fontSize="md" fontWeight="bold" mt={1} color="yellow.600">
                Storage
              </Text>
              <Text fontSize="xs" color="yellow.600">75% Used</Text>
              <Progress value={75} colorScheme="yellow" size="sm" mt={2} />
            </Box>
            <Box textAlign="center" p={3} border="1px" borderColor="green.200" borderRadius="md" bg="green.50">
              <FiGlobe size={24} color="green" />
              <Text fontSize="md" fontWeight="bold" mt={1} color="green.600">
                CDN
              </Text>
              <Text fontSize="xs" color="green.600">Online</Text>
              <Progress value={98} colorScheme="green" size="sm" mt={2} />
            </Box>
          </SimpleGrid>
        </CardBody>
      </Card>
    </VStack>
  );

  const renderPerformance = () => (
    <VStack spacing={6} align="stretch">
      {lighthouseMetrics?.available ? (
        <>
          {/* Lighthouse Overview with Refresh Button */}
          <Card bg={cardBg} border="1px" borderColor={borderColor}>
            <CardBody>
              <Flex justify="space-between" align="center" mb={4}>
                <Box>
                  <Heading size="md">Lighthouse Performance Overview</Heading>
                  <HStack spacing={2} mt={2}>
                    <Badge colorScheme="blue" variant="outline">
                      v{lighthouseMetrics.lighthouseVersion}
                    </Badge>
                    <Badge colorScheme="green" variant="outline">
                      Latest Report
                    </Badge>
                  </HStack>
                </Box>
                <VStack spacing={2}>
                  <Button
                    size="sm"
                    colorScheme="blue"
                    onClick={refreshLighthouseData}
                    isLoading={lighthouseLoading}
                    loadingText="Refreshing..."
                    leftIcon={<FiRefreshCw />}
                  >
                    Refresh Data
                  </Button>
                  <Text fontSize="xs" color="gray.500" textAlign="center">
                    Auto-refresh in 5min
                  </Text>
                </VStack>
              </Flex>
              
              <VStack spacing={3} align="stretch">
                <Text fontSize="sm" color="gray.600">
                  <strong>Test URL:</strong> {lighthouseMetrics.testUrl}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  <strong>Last Scan:</strong> {new Date(lighthouseMetrics.fetchTime).toLocaleString()}
                </Text>
                <Flex justify="space-between" align="center">
                  <Text fontSize="sm" color="gray.600">
                    <strong>Overall Score:</strong> 
                  </Text>
                  <Badge 
                    size="lg" 
                    colorScheme={
                      lighthouseMetrics.overallScore >= 90 ? 'green' :
                      lighthouseMetrics.overallScore >= 70 ? 'orange' : 'red'
                    }
                    px={3}
                    py={1}
                    fontSize="md"
                  >
                    {lighthouseMetrics.overallScore}/100
                  </Badge>
                </Flex>
              </VStack>
            </CardBody>
          </Card>

          {/* Category Scores - Enhanced */}
          <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
            {Object.entries(lighthouseMetrics.categories).map(([key, category]: [string, any]) => (
              <Card key={key} bg={cardBg} border="1px" borderColor={borderColor} position="relative">
                <CardBody textAlign="center">
                  {/* Category improvement indicator */}
                  {category.score >= 90 && (
                    <Box position="absolute" top={2} right={2}>
                      <Badge colorScheme="green" size="sm">✓</Badge>
                    </Box>
                  )}
                  <Text fontSize="sm" color="gray.600" mb={2}>
                    {category.title}
                  </Text>
                  <Text 
                    fontSize="3xl" 
                    fontWeight="bold"
                    color={
                      category.score >= 90 ? 'green.500' :
                      category.score >= 70 ? 'orange.500' : 'red.500'
                    }
                  >
                    {category.score}
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    out of 100
                  </Text>
                  {/* Performance target indicators */}
                  <Text fontSize="xs" color="gray.400" mt={1}>
                    Target: {key === 'performance' ? '90+' : key === 'seo' ? '95+' : '95+'}
                  </Text>
                </CardBody>
              </Card>
            ))}
          </Grid>

          {/* Core Web Vitals - Enhanced */}
          <Card bg={cardBg} border="1px" borderColor={borderColor}>
            <CardBody>
              <Flex justify="space-between" align="center" mb={4}>
                <Heading size="md">Core Web Vitals</Heading>
                <Badge colorScheme="purple" variant="outline">
                  Google Standards
                </Badge>
              </Flex>
              <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
                {Object.entries(lighthouseMetrics.coreWebVitals).map(([key, vital]: [string, any]) => {
                  // Define targets for each metric
                  const targets = {
                    firstContentfulPaint: { good: 1800, needsImprovement: 3000 },
                    largestContentfulPaint: { good: 2500, needsImprovement: 4000 },
                    totalBlockingTime: { good: 200, needsImprovement: 600 },
                    cumulativeLayoutShift: { good: 0.1, needsImprovement: 0.25 },
                    speedIndex: { good: 3400, needsImprovement: 5800 },
                    interactive: { good: 3800, needsImprovement: 7300 }
                  };
                  
                  const target = targets[key as keyof typeof targets];
                  let status = 'poor';
                  if (target) {
                    if (vital.value <= target.good) status = 'good';
                    else if (vital.value <= target.needsImprovement) status = 'needs-improvement';
                  }
                  
                  return (
                    <Box key={key} p={4} border="1px" borderColor={borderColor} borderRadius="md" bg="gray.50">
                      <Flex justify="space-between" align="start" mb={2}>
                        <Text fontSize="sm" fontWeight="medium">
                          {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </Text>
                        <Badge 
                          size="xs"
                          colorScheme={
                            status === 'good' ? 'green' :
                            status === 'needs-improvement' ? 'orange' : 'red'
                          }
                        >
                          {status.replace('-', ' ')}
                        </Badge>
                      </Flex>
                      <Text fontSize="lg" fontWeight="bold" 
                        color={
                          vital.score >= 0.9 ? 'green.500' :
                          vital.score >= 0.6 ? 'orange.500' : 'red.500'
                        }
                      >
                        {vital.displayValue}
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        Score: {Math.round(vital.score * 100)}/100
                      </Text>
                      {target && (
                        <Text fontSize="xs" color="gray.400" mt={1}>
                          Target: &lt;{target.good}{key.includes('layoutShift') ? '' : 'ms'}
                        </Text>
                      )}
                    </Box>
                  );
                })}
              </Grid>
            </CardBody>
          </Card>

          {/* Performance Opportunities - Enhanced */}
          <Card bg={cardBg} border="1px" borderColor={borderColor}>
            <CardBody>
              <Flex justify="space-between" align="center" mb={4}>
                <Heading size="md">Performance Opportunities</Heading>
                <Badge colorScheme="orange" variant="outline">
                  {lighthouseMetrics.opportunities.filter((opp: any) => opp.score < 0.9).length} Issues Found
                </Badge>
              </Flex>
              <TableContainer>
                <Table size="sm">
                  <Thead>
                    <Tr>
                      <Th>Opportunity</Th>
                      <Th>Priority</Th>
                      <Th>Status</Th>
                      <Th>Potential Savings</Th>
                      <Th>Current Value</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {lighthouseMetrics.opportunities
                      .sort((a: any, b: any) => b.potentialSavings - a.potentialSavings)
                      .map((opp: any, index: number) => (
                      <Tr key={index}>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="medium" fontSize="sm">{opp.title}</Text>
                            <Text fontSize="xs" color="gray.500" noOfLines={2}>
                              {opp.description}
                            </Text>
                          </VStack>
                        </Td>
                        <Td>
                          <Badge 
                            size="sm"
                            colorScheme={
                              opp.potentialSavings > 1000 ? 'red' :
                              opp.potentialSavings > 500 ? 'orange' : 'yellow'
                            }
                          >
                            {opp.potentialSavings > 1000 ? 'High' :
                             opp.potentialSavings > 500 ? 'Medium' : 'Low'}
                          </Badge>
                        </Td>
                        <Td>
                          <Badge 
                            colorScheme={
                              opp.score === 1 ? 'green' :
                              opp.score >= 0.5 ? 'yellow' : 'red'
                            }
                          >
                            {opp.score === 1 ? 'Passed' :
                             opp.score >= 0.5 ? 'Needs Improvement' : 'Failed'}
                          </Badge>
                        </Td>
                        <Td fontWeight="medium">
                          {opp.potentialSavings > 0 ? (
                            <Text color={opp.potentialSavings > 1000 ? 'red.500' : 'orange.500'}>
                              {Math.round(opp.potentialSavings)}ms
                            </Text>
                          ) : 'N/A'}
                        </Td>
                        <Td>{opp.displayValue || 'N/A'}</Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </CardBody>
          </Card>

          {/* SEO Optimization Status - New */}
          <Card bg={cardBg} border="1px" borderColor={borderColor}>
            <CardBody>
              <Flex justify="space-between" align="center" mb={4}>
                <Heading size="md">SEO & Technical Optimization</Heading>
                <Badge colorScheme="blue" variant="outline">
                  Recently Updated
                </Badge>
              </Flex>
              <Grid templateColumns="repeat(2, 1fr)" gap={6}>
                <VStack align="start" spacing={3}>
                  <Text fontWeight="medium">SEO Enhancements</Text>
                  <VStack align="start" spacing={1}>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">robots.txt implemented</Text>
                    </Flex>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">sitemap.xml created</Text>
                    </Flex>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">Meta tags optimized</Text>
                    </Flex>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">Structured data added</Text>
                    </Flex>
                  </VStack>
                </VStack>
                <VStack align="start" spacing={3}>
                  <Text fontWeight="medium">Performance Optimizations</Text>
                  <VStack align="start" spacing={1}>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">Code splitting implemented</Text>
                    </Flex>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">Lazy loading enabled</Text>
                    </Flex>
                    <Flex align="center">
                      <Badge colorScheme="green" size="sm" mr={2}>✓</Badge>
                      <Text fontSize="sm">Critical CSS inlined</Text>
                    </Flex>
                    <Flex align="center">
                      <Badge colorScheme="orange" size="sm" mr={2}>⚠</Badge>
                      <Text fontSize="sm">Bundle optimization pending</Text>
                    </Flex>
                  </VStack>
                </VStack>
              </Grid>
            </CardBody>
          </Card>
        </>
      ) : (
        <Card bg={cardBg} border="1px" borderColor={borderColor}>
          <CardBody textAlign="center" py={8}>
            <VStack spacing={4}>
              <Text fontSize="lg" mb={2}>Lighthouse Report Not Available</Text>
              <Text color="gray.600" mb={4}>
                {lighthouseMetrics?.message || 'No Lighthouse data found.'}
              </Text>
              <Button
                colorScheme="blue"
                onClick={refreshLighthouseData}
                isLoading={lighthouseLoading}
                loadingText="Checking..."
                leftIcon={<FiRefreshCw />}
              >
                Check for Reports
              </Button>
              <Box>
                <Text fontSize="sm" color="gray.500" mb={2}>
                  Generate a new Lighthouse report:
                </Text>
                <Box bg="gray.100" p={3} borderRadius="md">
                  <Text fontSize="sm" fontFamily="mono">
                    lighthouse https://careerdart.com --output=json --output-path=./lighthouse-final.json
                  </Text>
                </Box>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      )}
    </VStack>
  );

  const renderSecurity = () => (
    <VStack spacing={6} align="stretch">
      {/* Security Overview */}
      <Card bg={cardBg} border="1px" borderColor={borderColor}>
        <CardHeader>
          <Heading size="lg">Security & Audit Logs</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} mb={6}>
            <Box textAlign="center" p={4} border="1px" borderColor="green.200" borderRadius="md" bg="green.50">
              <FiShield size={40} color="green" />
              <Text fontSize="lg" fontWeight="bold" mt={2} color="green.600">
                Security Status
              </Text>
              <Text fontSize="sm" color="green.600">Secure</Text>
            </Box>
            <Box textAlign="center" p={4} border="1px" borderColor="blue.200" borderRadius="md" bg="blue.50">
              <FiShield size={40} color="blue" />
              <Text fontSize="lg" fontWeight="bold" mt={2} color="blue.600">
                Failed Logins
              </Text>
              <Text fontSize="sm" color="blue.600">3 Today</Text>
            </Box>
            <Box textAlign="center" p={4} border="1px" borderColor="orange.200" borderRadius="md" bg="orange.50">
              <FiAlertTriangle size={40} color="orange" />
              <Text fontSize="lg" fontWeight="bold" mt={2} color="orange.600">
                Alerts
              </Text>
              <Text fontSize="sm" color="orange.600">{errorLogs.filter(log => log.severity === 'high').length} High Priority</Text>
            </Box>
          </SimpleGrid>

          {/* Error Logs */}
          <VStack spacing={4} align="stretch">
            <HStack justify="space-between">
              <Heading size="md">Recent Error Logs</Heading>
              <Select
                placeholder="Filter by severity"
                value={errorSeverityFilter}
                onChange={(e) => setErrorSeverityFilter(e.target.value)}
                maxW="200px"
                size="sm"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </Select>
            </HStack>

            <TableContainer>
              <Table size="sm">
                <Thead>
                  <Tr>
                    <Th>Severity</Th>
                    <Th>Type</Th>
                    <Th>Message</Th>
                    <Th>User</Th>
                    <Th>Time</Th>
                    <Th>Status</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {errorLogs
                    .filter(error => !errorSeverityFilter || error.severity === errorSeverityFilter)
                    .slice(0, 10)
                    .map((error, index) => (
                    <Tr key={index}>
                      <Td>
                        <Badge 
                          colorScheme={
                            error.severity === 'critical' ? 'red' :
                            error.severity === 'high' ? 'orange' :
                            error.severity === 'medium' ? 'yellow' : 'gray'
                          }
                          size="sm"
                        >
                          {error.severity}
                        </Badge>
                      </Td>
                      <Td>{error.errorType}</Td>
                      <Td maxW="300px" isTruncated>
                        <Tooltip label={error.message}>
                          {error.message}
                        </Tooltip>
                      </Td>
                      <Td>{error.userId || 'System'}</Td>
                      <Td>{new Date(error.timestamp).toLocaleString()}</Td>
                      <Td>
                        <Badge colorScheme={error.resolved ? 'green' : 'red'} size="sm">
                          {error.resolved ? 'Resolved' : 'Open'}
                        </Badge>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
          </VStack>
        </CardBody>
      </Card>
    </VStack>
  );

  const renderSettings = () => (
    <VStack spacing={6} align="stretch">
      <Card bg={cardBg} border="1px" borderColor={borderColor}>
        <CardHeader>
          <Heading size="lg">System Settings</Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={6} align="stretch">
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="maintenance-mode" mb="0">
                Maintenance Mode
              </FormLabel>
              <Switch id="maintenance-mode" />
            </FormControl>
            
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="email-notifications" mb="0">
                Email Notifications
              </FormLabel>
              <Switch id="email-notifications" defaultChecked />
            </FormControl>
            
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="auto-backup" mb="0">
                Automatic Backups
              </FormLabel>
              <Switch id="auto-backup" defaultChecked />
            </FormControl>

            <Button colorScheme="blue" size="sm" alignSelf="start">
              Save Settings
            </Button>
          </VStack>
        </CardBody>
      </Card>
    </VStack>
  );

  // User Modal Component
  const UserModal = () => (
    <Modal isOpen={isUserModalOpen} onClose={onUserModalClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>User Details</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          {selectedUser && (
            <VStack spacing={4} align="stretch">
              <HStack spacing={4}>
                <Avatar size="xl" name={selectedUser.username} />
                <VStack spacing={1} align="start">
                  <HStack spacing={2}>
                    <Text fontSize="xl" fontWeight="bold">{selectedUser.username}</Text>
                    {selectedUser.bio?.includes('[VERIFIED]') && (
                      <Badge colorScheme="green">Verified</Badge>
                    )}
                  </HStack>
                  <Text color="gray.600">{selectedUser.email}</Text>
                  <Badge colorScheme={selectedUser.hasPaid ? 'green' : 'gray'}>
                    {selectedUser.hasPaid ? 'Premium' : 'Free'}
                  </Badge>
                </VStack>
              </HStack>
              
              <SimpleGrid columns={2} spacing={4}>
                <Box>
                  <Text fontSize="sm" color="gray.600">User ID</Text>
                  <Text fontWeight="medium">{selectedUser.id}</Text>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">Credits</Text>
                  <Text fontWeight="medium">{selectedUser.credits}</Text>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">Member Since</Text>
                  <Text fontWeight="medium">{new Date(selectedUser.createdAt).toLocaleDateString()}</Text>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">Last Login</Text>
                  <Text fontWeight="medium">
                    {selectedUser.lastLoginAt 
                      ? new Date(selectedUser.lastLoginAt).toLocaleDateString()
                      : 'Never'
                    }
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">Years of Experience</Text>
                  <Text fontWeight="medium">{selectedUser.yearsOfExperience || 'Not set'}</Text>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">GPT Model</Text>
                  <Text fontWeight="medium">{selectedUser.gptModel || 'Default'}</Text>
                </Box>
              </SimpleGrid>

              {selectedUser.bio && (
                <Box>
                  <Text fontSize="sm" color="gray.600">Bio</Text>
                  <Text fontWeight="medium">{selectedUser.bio.replace('[VERIFIED]', '').trim() || 'No bio'}</Text>
                </Box>
              )}

              <HStack spacing={3} pt={4}>
                <Button colorScheme="blue" size="sm" onClick={() => handleEditUser(selectedUser)}>
                  Edit User
                </Button>
                <Button colorScheme="green" size="sm" onClick={() => handleSendEmail(selectedUser)}>
                  Send Email
                </Button>
                <Button 
                  colorScheme={selectedUser.bio?.includes('[VERIFIED]') ? 'orange' : 'green'} 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleSetVerified(selectedUser, !selectedUser.bio?.includes('[VERIFIED]'))}
                >
                  {selectedUser.bio?.includes('[VERIFIED]') ? 'Remove Verification' : 'Verify User'}
                </Button>
                <Button colorScheme="red" variant="outline" size="sm" onClick={() => handleDeleteUser(selectedUser)}>
                  Delete
                </Button>
              </HStack>
            </VStack>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );

  // Edit User Modal Component
  const EditUserModal = () => (
    <Modal isOpen={isEditUserModalOpen} onClose={onEditUserModalClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Edit User Details</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          {editingUser && (
            <VStack spacing={4} align="stretch" as="form" onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const updates = {
                username: formData.get('username') as string,
                email: formData.get('email') as string,
                credits: parseInt(formData.get('credits') as string),
                bio: formData.get('bio') as string,
                yearsOfExperience: parseInt(formData.get('yearsOfExperience') as string) || 0,
                gptModel: formData.get('gptModel') as string,
              };
              submitUserUpdate(updates);
            }}>
              <SimpleGrid columns={2} spacing={4}>
                <FormControl>
                  <FormLabel>Username</FormLabel>
                  <Input name="username" defaultValue={editingUser.username} />
                </FormControl>
                <FormControl>
                  <FormLabel>Email</FormLabel>
                  <Input name="email" type="email" defaultValue={editingUser.email} />
                </FormControl>
                <FormControl>
                  <FormLabel>Credits</FormLabel>
                  <Input name="credits" type="number" defaultValue={editingUser.credits} />
                </FormControl>
                <FormControl>
                  <FormLabel>Years of Experience</FormLabel>
                  <Input name="yearsOfExperience" type="number" defaultValue={editingUser.yearsOfExperience || 0} />
                </FormControl>
              </SimpleGrid>
              
              <FormControl>
                <FormLabel>GPT Model</FormLabel>
                <Select name="gptModel" defaultValue={editingUser.gptModel}>
                  <option value="gpt-4o-mini">GPT-4o Mini</option>
                  <option value="gpt-4o">GPT-4o</option>
                  <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel>Bio</FormLabel>
                <Textarea 
                  name="bio" 
                  defaultValue={editingUser.bio?.replace('[VERIFIED]', '').trim() || ''} 
                  placeholder="User bio..."
                />
              </FormControl>

              <HStack spacing={3} pt={4}>
                <Button 
                  type="submit" 
                  colorScheme="blue" 
                  isLoading={actionLoading}
                  loadingText="Updating..."
                >
                  Update User
                </Button>
                <Button variant="ghost" onClick={onEditUserModalClose}>
                  Cancel
                </Button>
              </HStack>
            </VStack>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );

  // Send Email Modal Component
  const SendEmailModal = () => (
    <Modal isOpen={isEmailModalOpen} onClose={onEmailModalClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Send Email to {selectedUser?.username}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={4} align="stretch">
            <FormControl>
              <FormLabel>Recipient</FormLabel>
              <Input value={selectedUser?.email || ''} isReadOnly bg="gray.50" />
            </FormControl>
            
            <FormControl>
              <FormLabel>Email Type</FormLabel>
              <Select 
                value={emailData.emailType} 
                onChange={(e) => setEmailData(prev => ({ ...prev, emailType: e.target.value }))}
              >
                <option value="general">General</option>
                <option value="notification">Notification</option>
                <option value="warning">Warning</option>
                <option value="promotion">Promotion</option>
                <option value="support">Support</option>
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel>Subject</FormLabel>
              <Input 
                value={emailData.subject}
                onChange={(e) => setEmailData(prev => ({ ...prev, subject: e.target.value }))}
                placeholder="Email subject..."
              />
            </FormControl>

            <FormControl>
              <FormLabel>Message</FormLabel>
              <Textarea 
                value={emailData.message}
                onChange={(e) => setEmailData(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Email message..."
                rows={6}
              />
            </FormControl>

            <HStack spacing={3} pt={4}>
              <Button 
                colorScheme="green" 
                onClick={submitSendEmail}
                isLoading={actionLoading}
                loadingText="Sending..."
                isDisabled={!emailData.subject || !emailData.message}
              >
                Send Email
              </Button>
              <Button variant="ghost" onClick={onEmailModalClose}>
                Cancel
              </Button>
            </HStack>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );

  // Delete User Confirmation Modal
  const DeleteUserModal = () => (
    <Modal isOpen={isDeleteModalOpen} onClose={onDeleteModalClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader color="red.500">Delete User Account</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={4} align="stretch">
            <Alert status="warning">
              <AlertIcon />
              This action cannot be undone. Please confirm you want to delete this user account.
            </Alert>
            
            <Text>
              <strong>User:</strong> {deleteConfirmation.username}
            </Text>
            
            <Text fontSize="sm" color="gray.600">
              Choose deletion type:
            </Text>
            
            <VStack spacing={3} align="stretch">
              <Button
                colorScheme="orange"
                onClick={() => confirmDeleteUser(false)}
                isLoading={actionLoading}
                loadingText="Deactivating..."
              >
                Deactivate (Soft Delete)
              </Button>
              <Text fontSize="xs" color="gray.500" textAlign="center">
                User data will be anonymized but preserved
              </Text>
              
              <Button
                colorScheme="red"
                onClick={() => confirmDeleteUser(true)}
                isLoading={actionLoading}
                loadingText="Deleting..."
              >
                Permanently Delete
              </Button>
              <Text fontSize="xs" color="gray.500" textAlign="center">
                All user data will be permanently removed
              </Text>
            </VStack>

            <HStack spacing={3} pt={4}>
              <Button variant="ghost" onClick={onDeleteModalClose} flex={1}>
                Cancel
              </Button>
            </HStack>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );

  // Loading state
  if (loading) {
    return (
      <Center minH="100vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" />
          <Text>Loading admin dashboard...</Text>
        </VStack>
      </Center>
    );
  }

  // Access denied state - render without early return
  if (!adminData) {
    return (
      <Center minH="400px">
        <Alert status="error" maxW="md">
          <AlertIcon />
          Access denied. Admin privileges required.
        </Alert>
      </Center>
    );
  }

  // Top Navigation Component (responsive)
  const TopNavigation = () => (
    <Box
      bg={headerBg}
      borderBottom="2px"
      borderColor={borderColor}
      px={headerPadding}
      py={4}
      position="sticky"
      top={0}
      zIndex={1000}
      boxShadow="sm"
    >
      <VStack spacing={{ base: 2, md: 4 }} align="stretch">
        {/* Header Row with Title and Actions */}
        <Flex justify="space-between" align="center">
          <HStack spacing={{ base: 2, md: 4 }}>
            {/* Mobile hamburger menu */}
            {isMobile && (
              <IconButton
                icon={<FiMenu />}
                variant="ghost"
                size="sm"
                onClick={onMobileNavOpen}
                aria-label="Open navigation menu"
              />
            )}
            <Heading size={{ base: "md", md: "lg" }} color={activeColor}>
              {isMobile ? "CareerDart" : "CareerDart Admin"}
            </Heading>
            <Badge colorScheme="green" size="sm">Online</Badge>
          </HStack>

          <HStack spacing={{ base: 1, md: 4 }}>
            {/* Notifications */}
            <Menu>
              <MenuButton as={IconButton} icon={<FiBell />} variant="ghost" size="sm">
                <Badge colorScheme="red" variant="solid" fontSize="0.6em" position="absolute" top={-1} right={-1}>
                  {notifications.length}
                </Badge>
              </MenuButton>
              <MenuList>
                {notifications.map((notif) => (
                  <MenuItem key={notif.id} py={3}>
                    <VStack spacing={1} align="start">
                      <Text fontSize="sm">{notif.message}</Text>
                      <Text fontSize="xs" color="gray.500">{notif.time}</Text>
                    </VStack>
                  </MenuItem>
                ))}
              </MenuList>
            </Menu>

            {/* Refresh */}
            <IconButton
              icon={<FiRefreshCw />}
              variant="ghost"
              size="sm"
              onClick={loadDashboardData}
              isLoading={loading}
              aria-label="Refresh dashboard data"
            />

            {/* Settings */}
            {!isMobile && (
              <IconButton
                icon={<FiSettings />}
                variant="ghost"
                size="sm"
                onClick={onSettingsModalOpen}
                aria-label="Open settings"
              />
            )}

            {/* Admin Avatar */}
            <Avatar size="sm" name="Admin User" bg={activeColor} />
          </HStack>
        </Flex>

        {/* Navigation Tabs - Hidden on mobile (shown in drawer) */}
        {!isMobile && (
          <HStack spacing={2} overflowX="auto" pb={2}>
            {navItems.map((item) => (
              <Button
                key={item.id}
                leftIcon={<item.icon />}
                variant={activeSection === item.id ? "solid" : "ghost"}
                colorScheme={activeSection === item.id ? "blue" : "gray"}
                size="sm"
                minW="fit-content"
                onClick={() => {
                  if (item.href) {
                    window.location.href = item.href;
                  } else {
                    setActiveSection(item.id);
                  }
                }}
                bg={activeSection === item.id ? activeBg : 'transparent'}
                color={activeSection === item.id ? activeColor : 'inherit'}
                _hover={{ bg: hoverBg }}
                px={4}
                py={2}
                borderRadius="md"
                position="relative"
              >
                {isTablet ? item.label.split(' ')[0] : item.label}
                {item.id === 'feedback' && unreadFeedbackCount > 0 && (
                  <Badge
                    position="absolute"
                    top="-8px"
                    right="-8px"
                    colorScheme="red"
                    borderRadius="full"
                    fontSize="xs"
                    minW="20px"
                    h="20px"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    {unreadFeedbackCount > 99 ? '99+' : unreadFeedbackCount}
                  </Badge>
                )}
              </Button>
            ))}
          </HStack>
        )}

        {/* Breadcrumb and Status - Responsive */}
        <Flex 
          justify="space-between" 
          align="center" 
          pt={2}
          direction={{ base: "column", md: "row" }}
          gap={{ base: 2, md: 0 }}
        >
          <Breadcrumb spacing="8px" separator={<FiChevronRight />} fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink href="#">Admin</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>{navItems.find(item => item.id === activeSection)?.label}</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          
          <HStack spacing={{ base: 2, md: 4 }} fontSize={{ base: "xs", md: "sm" }}>
            {!isMobile && (
              <Text color="gray.500">
                Last updated: {new Date().toLocaleTimeString()}
              </Text>
            )}
            <HStack spacing={2}>
              <Progress value={85} size="sm" w={{ base: "60px", md: "100px" }} colorScheme="green" />
              <Text fontSize="xs" color="gray.600">Load: 85%</Text>
            </HStack>
          </HStack>
        </Flex>
      </VStack>
    </Box>
  );

  // Mobile Navigation Drawer
  const MobileNavDrawer = () => (
    <Drawer isOpen={isMobileNavOpen} placement="left" onClose={onMobileNavClose}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader borderBottomWidth="1px">
          <Heading size="md" color={activeColor}>Navigation</Heading>
        </DrawerHeader>
        <DrawerBody p={0}>
          <VStack spacing={0} align="stretch">
            {navItems.map((item) => (
              <Button
                key={item.id}
                leftIcon={<item.icon />}
                variant="ghost"
                justifyContent="flex-start"
                size="lg"
                py={6}
                borderRadius={0}
                bg={activeSection === item.id ? activeBg : 'transparent'}
                color={activeSection === item.id ? activeColor : 'inherit'}
                _hover={{ bg: hoverBg }}
                onClick={() => {
                  if (item.href) {
                    window.location.href = item.href;
                  } else {
                    setActiveSection(item.id);
                  }
                  onMobileNavClose();
                }}
                position="relative"
              >
                <HStack spacing={2} w="full" justify="space-between">
                  <Text>{item.label}</Text>
                  {item.id === 'feedback' && unreadFeedbackCount > 0 && (
                    <Badge
                      colorScheme="red"
                      borderRadius="full"
                      fontSize="xs"
                      minW="20px"
                      h="20px"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      {unreadFeedbackCount > 99 ? '99+' : unreadFeedbackCount}
                    </Badge>
                  )}
                </HStack>
              </Button>
            ))}
            <Divider />
            <Button
              leftIcon={<FiSettings />}
              variant="ghost"
              justifyContent="flex-start"
              size="lg"
              py={6}
              borderRadius={0}
              _hover={{ bg: hoverBg }}
              onClick={() => {
                onSettingsModalOpen();
                onMobileNavClose();
              }}
            >
              Settings
            </Button>
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );

  // Sidebar Component (removed - keeping for reference)
  const Sidebar = () => null;

  // Header Component (simplified since top nav handles most of it)
  const Header = () => null;

  // Main Content Area (responsive)
  const MainContent = () => (
    <Box p={contentPadding} bg={mainBg} minH="calc(100vh - 200px)">
      <Container maxW="95%" px={{ base: 2, md: 4, lg: 6 }}>
        {renderActiveSection()}
      </Container>
    </Box>
  );

  // Main render - full width layout with top navigation
  return (
    <Box bg={mainBg} minH="100vh" position="relative">
      {/* Back Button for Mobile */}
      <BackButton />
      
      <TopNavigation />
      <MainContent />
      <UserModal />
      <EditUserModal />
      <SendEmailModal />
      <DeleteUserModal />
      <MobileNavDrawer />
    </Box>
  );
};

export default AdminDashboard; 