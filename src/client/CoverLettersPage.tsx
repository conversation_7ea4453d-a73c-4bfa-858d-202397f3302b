import { type CoverLetter } from 'wasp/entities';
import { useQuery, getAllCoverLetters } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Heading,
  VStack,
  HStack,
  Text,
  Badge,
  Grid,
  GridItem,
  Tooltip,
  useColorModeValue,
  useBreakpointValue,
  Flex,
} from '@chakra-ui/react';
import { FaPlus, FaEdit, FaCopy } from 'react-icons/fa';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ActionButton from './components/ActionButton';
import ContentContainer from './components/ContentContainer';
import BackButton from './components/BackButton';
import { useState, useEffect } from 'react';
import { useClipboard } from '@chakra-ui/react';
import { EmptyCoverLetters } from './components/EmptyState';
import { SimpleLoading } from './components/LoadingState';

export default function CoverLettersPage() {
  const navigate = useNavigate();
  const [selectedCoverLetter, setSelectedCoverLetter] = useState<CoverLetter | null>(null);
  const { onCopy } = useClipboard(selectedCoverLetter?.content || '');
  const { data: user } = useAuth();

  // Color mode values
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  // Responsive values
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const containerPadding = useBreakpointValue({ base: 4, md: 8 });
  const gridTemplateColumns = useBreakpointValue({ 
    base: "1fr", 
    lg: "repeat(2, 1fr)" 
  });

  const { data: coverLetters, isLoading } = useQuery(getAllCoverLetters, undefined, { enabled: !!user });

  // Set the first cover letter as selected when data loads
  useEffect(() => {
    if (coverLetters && coverLetters.length > 0 && !selectedCoverLetter) {
      setSelectedCoverLetter(coverLetters[0]);
    }
  }, [coverLetters]);

  const handleCreateNew = () => {
    navigate('/?create=new');
  };

  const handleEdit = (id: string) => {
    navigate(`/cover-letters/${id}`);
  };

  const handlePreview = (coverLetter: CoverLetter) => {
    setSelectedCoverLetter(coverLetter);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString() + ' ' + new Date(date).toLocaleTimeString();
  };

  return (
    <Box bg={bgColor} minH="100vh" py={containerPadding} position="relative">
      {/* Back Button for Mobile */}
      <BackButton />
      
      <Container maxW={{ base: "100%", md: "95%" }} px={{ base: 2, md: 4, lg: 6 }}>
        <VStack spacing={{ base: 4, md: 6 }} align="stretch" width="100%">
          <PageHeader
            title="Cover Letters"
            subtitle="Manage all your cover letters in one place"
          >
            <ActionButton
              icon={FaPlus}
              label="Create New"
              variant="primary"
              onClick={handleCreateNew}
              size={{ base: "sm", md: "md" }}
            />
          </PageHeader>

          {isLoading ? (
            <SimpleLoading message="Loading your cover letters..." />
          ) : !coverLetters || coverLetters.length === 0 ? (
            <EmptyCoverLetters
              primaryAction={{
                label: 'Create Cover Letter',
                onClick: handleCreateNew,
                icon: <FaPlus />,
                colorScheme: 'blue',
              }}
            />
          ) : (
            <Grid templateColumns={gridTemplateColumns} gap={{ base: 4, md: 6 }}>
              {/* Left side - List of cover letters */}
              <GridItem>
                <ContentContainer
                  delay={0.3}
                  maxH={{ base: "300px", md: "400px", lg: "calc(100vh - 200px)" }}
                  overflowY="auto"
                >
                  <VStack spacing={2} align="stretch">
                    {coverLetters.map((coverLetter) => (
                    <Box
                      key={coverLetter.id}
                      p={{ base: 3, md: 3 }}
                      borderRadius={{ base: "lg", md: "md" }}
                      borderLeft={coverLetter.id === selectedCoverLetter?.id ? '3px solid' : 'none'}
                      borderColor={coverLetter.id === selectedCoverLetter?.id ? 'purple.400' : 'transparent'}
                      bg={coverLetter.id === selectedCoverLetter?.id ?
                        useColorModeValue('purple.50', 'purple.900') : 'transparent'}
                      cursor="pointer"
                      onClick={() => handlePreview(coverLetter)}
                      _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                      transition="all 0.15s"
                      boxShadow={coverLetter.id === selectedCoverLetter?.id ? "sm" : "none"}
                      mb={1}
                      border="1px solid"
                      borderColor={coverLetter.id === selectedCoverLetter?.id ?
                        'purple.200' : useColorModeValue('gray.200', 'gray.600')}
                    >
                      <VStack align="stretch" spacing={2}>
                        <Flex 
                          direction={{ base: 'column', sm: 'row' }}
                          justify="space-between" 
                          align={{ base: 'flex-start', sm: 'flex-start' }}
                          gap={{ base: 2, sm: 0 }}
                        >
                          <Heading size={{ base: "xs", md: "sm" }} fontWeight="medium" flex={1}>
                            {coverLetter.title}
                          </Heading>
                          <Badge colorScheme="purple" fontSize="xs" variant="subtle">
                            {formatDate(coverLetter.createdAt)}
                          </Badge>
                        </Flex>
                        <Text 
                          noOfLines={{ base: 2, md: 1 }} 
                          fontSize={{ base: "xs", md: "sm" }} 
                          color="gray.600"
                        >
                          {coverLetter.content.substring(0, 80)}...
                        </Text>
                        <HStack spacing={2} justify="flex-end">
                          <Tooltip label="Edit Cover Letter">
                            <span>
                              <ActionButton
                                icon={FaEdit}
                                label=""
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEdit(coverLetter.id);
                                }}
                              />
                            </span>
                          </Tooltip>
                          <Tooltip label="Copy to Clipboard">
                            <span>
                              <ActionButton
                                icon={FaCopy}
                                label=""
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onCopy();
                                }}
                              />
                            </span>
                          </Tooltip>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}
                  </VStack>
                </ContentContainer>
              </GridItem>

              {/* Right side - Cover letter preview */}
              <GridItem display={{ base: selectedCoverLetter ? "block" : "none", lg: "block" }}>
                {selectedCoverLetter ? (
                  <ContentContainer
                    height={{ base: "300px", md: "400px", lg: "calc(100vh - 200px)" }}
                    overflowY="auto"
                    delay={0.4}
                  >
                    <VStack align="stretch" spacing={{ base: 3, md: 6 }}>
                      <Flex
                        justify="space-between"
                        align="center"
                        mb={{ base: 2, md: 3 }}
                        direction={{ base: "column", sm: "row" }}
                        gap={{ base: 2, sm: 0 }}
                      >
                        <Heading size={{ base: "sm", md: "md" }} fontWeight="semibold">
                          {selectedCoverLetter.title}
                        </Heading>
                        <HStack spacing={2} display={{ base: "flex", lg: "none" }}>
                          <ActionButton
                            icon={FaEdit}
                            label="Edit"
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(selectedCoverLetter.id)}
                          />
                        </HStack>
                      </Flex>
                      <Box>
                        <Text
                          whiteSpace="pre-wrap"
                          fontSize={{ base: "sm", md: "md" }}
                          lineHeight={{ base: "1.5", md: "1.6" }}
                          color={useColorModeValue('gray.700', 'gray.300')}
                        >
                          {selectedCoverLetter.content}
                        </Text>
                      </Box>
                    </VStack>
                  </ContentContainer>
                ) : (
                  <ContentContainer
                    height={{ base: "300px", md: "400px", lg: "calc(100vh - 200px)" }}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    delay={0.4}
                  >
                    <VStack spacing={4} textAlign="center">
                      <Text fontSize={{ base: "sm", md: "md" }} color="gray.500">
                        Select a cover letter to preview
                      </Text>
                      <Text fontSize={{ base: "xs", md: "sm" }} color="gray.400">
                        Choose from your cover letters on the left
                      </Text>
                    </VStack>
                  </ContentContainer>
                )}
              </GridItem>
            </Grid>
          )}
        </VStack>
      </Container>
    </Box>
  );
}