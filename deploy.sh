#!/bin/bash

echo "🚀 Starting CareerDart deployment process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if wasp is installed
if ! command -v wasp &> /dev/null; then
    print_error "Wasp is not installed. Please install Wasp first."
    exit 1
fi

print_status "Building Wasp application..."

# Build the application
if ! wasp build; then
    print_error "Wasp build failed!"
    exit 1
fi

print_status "Wasp build completed successfully!"

# Navigate to web-app directory and install terser
print_status "Installing terser for production build..."
cd .wasp/build/web-app

if ! npm install --save-dev terser; then
    print_error "Failed to install terser!"
    exit 1
fi

# Build the frontend
print_status "Building frontend for production..."
if ! npm run build; then
    print_error "Frontend build failed!"
    exit 1
fi

print_status "Frontend build completed successfully!"

# Go back to root directory
cd ../../..

print_status "Build process completed! 🎉"
echo ""
echo "📁 Your built files are located at:"
echo "   Frontend: .wasp/build/web-app/build/"
echo "   Backend:  .wasp/build/server/"
echo ""
echo "📋 Next steps:"
echo "1. Deploy backend to Render (see DEPLOYMENT_GUIDE.md)"
echo "2. Deploy frontend to Netlify (see DEPLOYMENT_GUIDE.md)"
echo "3. Update environment variables"
echo "4. Test your deployed application"
echo ""
print_status "Happy deploying! 🚀" 